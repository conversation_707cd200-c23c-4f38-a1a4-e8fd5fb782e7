import { NextRequest, NextResponse } from 'next/server';
import { subDays, format } from 'date-fns';
import { normalizeData } from '@/utils/crmUtils';

const BATCH_SIZE = 200; // Número de registros por página

interface PersonData {
  id: number;
  contactPhones: Array<{
    id: number;
    phone: string;
    is_main: number;
  }>;
  contactEmails: Array<{
    id: number;
    email: string;
    is_main: number;
  }>;
}

// Cache em memória para evitar requisições duplicadas na mesma sessão
const personCache = new Map<number, PersonData>();

// Função de delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

interface PipeRunResponse<T> {
  data: T[];
  meta: {
    total: number;
    count: number;
    per_page: number;
    current_page: number;
    total_pages: number;
    links?: {
      next?: string;
    };
  };
}

async function fetchAllPersonsForPeriod(dateStart: string): Promise<Map<number, PersonData>> {
  try {
    // Se não temos pessoas em cache para esta data, buscamos todas
    const firstPageResponse = await fetch(
      `https://api.pipe.run/v1/persons?show=${BATCH_SIZE}&with=contactPhones,contactEmails&created_at_start=${dateStart}`,
      {
        headers: {
          'accept': 'application/json',
          'token': process.env.CRM_TOKEN || ''
        }
      }
    );

    if (!firstPageResponse.ok) {
      throw new Error('Falha ao buscar pessoas');
    }

    const firstPageData: PipeRunResponse<PersonData> = await firstPageResponse.json();
    
    // Armazena as pessoas da primeira página no cache
    firstPageData.data.forEach(person => {
      if (person.id) {
        personCache.set(person.id, person);
      }
    });

    // Se há mais páginas, busca todas
    const totalPages = firstPageData.meta.total_pages;

    if (totalPages > 1) {
      const remainingPages = Array.from({ length: totalPages - 1 }, (_, i) => i + 2);
      
      for (const page of remainingPages) {
        const pageResponse = await fetch(
          `https://api.pipe.run/v1/persons?show=${BATCH_SIZE}&with=contactPhones,contactEmails&created_at_start=${dateStart}&page=${page}`,
          {
            headers: {
              'accept': 'application/json',
              'token': process.env.CRM_TOKEN || ''
            }
          }
        );

        if (pageResponse.ok) {
          const pageData: PipeRunResponse<PersonData> = await pageResponse.json();
          pageData.data.forEach(person => {
            if (person.id) {
              personCache.set(person.id, person);
            }
          });
        }

        // Delay entre páginas para evitar rate limit
        await delay(1000);
      }
    }

    return personCache;
  } catch (error) {
    console.error('Erro ao buscar pessoas:', error);
    return personCache; // Retorna o cache mesmo em caso de erro
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { ownerId: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '1';
    const ownerId = params.ownerId;
    
    // Obtém a data de início e fim dos parâmetros ou usa o dia atual como padrão
    const dateStart = searchParams.get('dateStart') || format(new Date(), 'yyyy-MM-dd');
    const dateEnd = searchParams.get('dateEnd') || format(new Date(), 'yyyy-MM-dd');

    // Primeiro busca todas as pessoas do período
    console.log(`Buscando pessoas criadas entre ${dateStart} e ${dateEnd}`);
    const personsMap = await fetchAllPersonsForPeriod(dateStart);

    // Depois busca as oportunidades do vendedor
    const response = await fetch(
      `https://api.pipe.run/v1/deals?show=${BATCH_SIZE}&pipeline_id=56894&with=customFields,city,stage,origin,person,owner&deleted=false&freezed=false&owner_id=${ownerId}&page=${page}&created_at_start=${dateStart}&created_at_end=${dateEnd}&sort=-created_at`,
      {
        headers: {
          accept: "application/json",
          token: process.env.CRM_TOKEN || "",
        },
      }
    );

    if (!response.ok) {
      throw new Error('Falha ao buscar oportunidades');
    }

    const data = await response.json();
    
    // Enriquece os dados do deal com os dados completos do person antes de normalizar
    const enrichedDeals = data.data.map((deal: any) => {
      const personDetails = deal.person_id ? personsMap.get(deal.person_id) : null;
      return {
        ...deal,
        person: {
          ...deal.person,
          contactPhones: personDetails?.contactPhones || [],
          contactEmails: personDetails?.contactEmails || []
        }
      };
    });

    // Normaliza os dados usando a função do crmUtils com os dados enriquecidos
    const normalizedData = normalizeData(enrichedDeals);

    // Mapeia os dados normalizados para o formato que precisamos
    const opportunities = normalizedData.map((deal) => ({
      crmId: deal.crmId.toString(),
      brandTitle: deal.titulo,
      stageId: deal.stage_id?.toString() || '',
      stageName: deal.etapa || '',
      formData: {
        score: deal.score,
        businessInternship: deal.faseNegocio,
        dataCadastro: deal.dataCadastro,
        entradaLead: deal.entradaLead,
        apresentacao: deal.apresentacao,  
        leadNumber: deal.fone,
        nomeLead: deal.nomeLead,
        deleted: deal.deleted,
        origem: deal.origem,
        cidade: deal.cidade,
        uf: deal.uf,
        dataFechamento: deal.dataFechamento,
        horaVisita: deal.horaVisita,
        horaConversao: deal.horaConversao,
        cpf: deal.cpf,
        cnpj: deal.cnpj,
        status: deal.status,
        situacao: deal.situacao,
        valor: deal.valor,
        leadTime: deal.leadTime,
        temLogotipo: deal.temLogotipo,
        canal: deal.canal,
        tipoNegocio: deal.tipoNegocio,
        colaboradores: deal.colaboradores,
        urlConversao: deal.urlConversao,
        temCNPJ: deal.temCNPJ,
        campanha: deal.campanha,
        tipo: deal.tipo,
        conteudo: deal.conteudo,
        keyword: deal.keyword,
        origemCampanha: deal.origemCampanha,
        classes: deal.classes,
        indicadoPor: deal.indicadoPor,
        porte: deal.porte,
        comoFoiAtendido: deal.comoFoiAtendido,
        motivoPerda: deal.motivoPerda,
        formaPagamento: deal.formaPagamento,
        primeiroVencimento: deal.primeiroVencimento,
        valorCobrar: deal.valorCobrar,
        condicaoPagamento: deal.condicaoPagamento,
        parcelas: deal.parcelas,
        valorParcela: deal.valorParcela,
        valorDemaisParcelas: deal.valorDemaisParcelas,
        dataSegundoVencimento: deal.dataSegundoVencimento,
        protocolo: deal.protocolo,
        viabilidade: deal.viabilidade,
        rangeScore: deal.rangeScore,
        negociacao: deal.negociacao,
        tipoLead: deal.tipoLead,
        tokenSite: deal.tokenSite,
        pTime: deal.pTime,
        proposta: deal.proposta,
        idOriginal: deal.idOriginal,
        faseDoPlanejamento: deal.faseDoPlanejamento,
        tempoResolver: deal.tempoResolver,
        investiuMarca: deal.investiuMarca,
        ramoAtuacao: deal.ramoAtuacao,
        redesSociaisSite: deal.redesSociaisSite,
        tipoNegocioLead: deal.tipoNegocioLead,
        analiseCredito: deal.analiseCredito,
        plano: deal.plano,
        motivoRegistro: deal.motivoRegistro,
        dispositivo: deal.dispositivo,
        utmFinal: deal.utmFinal,
        historicoCampanhas: deal.historicoCampanhas
      }
    }));

    return NextResponse.json({
      success: true,
      data: opportunities,
      pagination: {
        total: data.meta.total,
        count: data.meta.count,
        perPage: data.meta.per_page,
        currentPage: data.meta.current_page,
        totalPages: data.meta.total_pages,
        hasNextPage: !!data.meta.links?.next,
        dateStart,
        dateEnd
      }
    });

  } catch (error) {
    console.error('Erro ao buscar oportunidades:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro ao buscar oportunidades'
      },
      { status: 500 }
    );
  }
} 
