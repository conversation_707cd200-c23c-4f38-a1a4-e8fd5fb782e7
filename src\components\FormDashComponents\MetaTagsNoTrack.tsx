// components/Head/MetaTags.tsx
import Head from "next/head";

interface MetaTagsProps {
  title: string;
  description: string;
  keywords: string;
  twitterCardType: string;
}

const   MetaTags = ({
  title,
  description,
  keywords,
  twitterCardType,
}: MetaTagsProps) => (
  <Head>
    <title>{title}</title>
    <meta name="description" content={description} />
    <meta name="keywords" content={keywords} />
    <meta name="robots" content="noindex, nofollow" />{" "}
    {/* Desativa indexação e rastreamento */}
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content={twitterCardType} />
    <meta name="twitter:title" content={title} />
    <meta name="twitter:description" content={description} />
  </Head>
);

export default MetaTags;
