import React from 'react';
import { format, subDays, parseISO, endOfDay, startOfDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { FaCalendar } from 'react-icons/fa';
import DatePicker, { registerLocale } from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";

// Registra o locale ptBR para o DatePicker
registerLocale('pt-BR', ptBR);

interface DateRange {
  start: string;
  end: string;
}

interface DateFilterProps {
  onDateChange: (dateRange: DateRange) => void;
  selectedDate: string;
}

const DateFilter: React.FC<DateFilterProps> = ({ onDateChange, selectedDate }) => {
  const handleDateSelect = (date: string, isPreset: boolean = false) => {
    if (isPreset) {
      // Para botões predefinidos, definimos start e end apropriadamente
      switch (date) {
        case format(new Date(), 'yyyy-MM-dd'): // Hoje
          onDateChange({
            start: format(startOfDay(new Date()), 'yyyy-MM-dd'),
            end: format(endOfDay(new Date()), 'yyyy-MM-dd')
          });
          break;
        case format(subDays(new Date(), 1), 'yyyy-MM-dd'): // Ontem
          const ontem = subDays(new Date(), 1);
          onDateChange({
            start: format(startOfDay(ontem), 'yyyy-MM-dd'),
            end: format(endOfDay(ontem), 'yyyy-MM-dd')
          });
          break;
        case format(subDays(new Date(), 7), 'yyyy-MM-dd'): // Essa Semana
          onDateChange({
            start: format(startOfDay(subDays(new Date(), 7)), 'yyyy-MM-dd'),
            end: format(endOfDay(new Date()), 'yyyy-MM-dd')
          });
          break;
      }
    } else {
      // Para data específica, usamos a mesma data como início e fim
      onDateChange({
        start: format(startOfDay(parseISO(date)), 'yyyy-MM-dd'),
        end: format(endOfDay(parseISO(date)), 'yyyy-MM-dd')
      });
    }
  };

  const buttonStyle = (isSelected: boolean) => `
    px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200
    ${isSelected 
      ? 'bg-blue-500 text-white shadow-sm hover:bg-blue-600' 
      : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 border border-gray-200 dark:border-gray-600'
    }
  `;

  return (
    <div className="flex items-center gap-2">
      <div className="flex gap-2">
        <button
          onClick={() => handleDateSelect(format(new Date(), 'yyyy-MM-dd'), true)}
          className={buttonStyle(selectedDate === format(new Date(), 'yyyy-MM-dd'))}
        >
          Hoje
        </button>
        <button
          onClick={() => handleDateSelect(format(subDays(new Date(), 1), 'yyyy-MM-dd'), true)}
          className={buttonStyle(selectedDate === format(subDays(new Date(), 1), 'yyyy-MM-dd'))}
        >
          Ontem
        </button>
        <button
          onClick={() => handleDateSelect(format(subDays(new Date(), 7), 'yyyy-MM-dd'), true)}
          className={buttonStyle(selectedDate === format(subDays(new Date(), 7), 'yyyy-MM-dd'))}
        >
          Essa Semana
        </button>
      </div>
      <div className="relative">
        <DatePicker
          selected={parseISO(selectedDate)}
          onChange={(date: Date | null) => {
            if (date) {
              handleDateSelect(format(date, 'yyyy-MM-dd'));
            }
          }}
          dateFormat="dd/MM/yyyy"
          locale="pt-BR"
          maxDate={new Date()}
          showPopperArrow={false}
          customInput={
            <button
              type="button"
              className={`
                flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-all duration-200
                bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 
                hover:bg-gray-50 dark:hover:bg-gray-600 
                border border-gray-200 dark:border-gray-600
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800
              `}
            >
              <span>{format(parseISO(selectedDate), 'dd/MM/yyyy')}</span>
              <FaCalendar className="text-gray-400 dark:text-gray-500" />
            </button>
          }
          calendarClassName="absolute z-50"
        />
      </div>
    </div>
  );
};

export default DateFilter; 