import React from "react";
import { Input } from "@/components/ui/input";

interface FilterComponentProps {
  scoreFilter: { min: number | ""; max: number | "" };
  setScoreFilter: React.Dispatch<React.SetStateAction<{ min: number | ""; max: number | "" }>>;
  _formProgressFilter: string;
  setFormProgressFilter: React.Dispatch<React.SetStateAction<string>>;
  _uniqueFormProgressValues: string[];
  _uniqueBrandTitles: string[];
  _brandTitleFilter: string;
  setBrandTitleFilter: React.Dispatch<React.SetStateAction<string>>;
}

const FilterComponent: React.FC<FilterComponentProps> = ({
  scoreFilter,
  setScoreFilter,
  _formProgressFilter,
  setFormProgressFilter,
  _uniqueFormProgressValues,
  _uniqueBrandTitles,
  _brandTitleFilter,
  setBrandTitleFilter,
}) => {
  const handleScoreMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value === "" ? "" : Number(e.target.value);
    setScoreFilter((prev) => ({ ...prev, min: value }));
  };

  const handleScoreMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value === "" ? "" : Number(e.target.value);
    setScoreFilter((prev) => ({ ...prev, max: value }));
  };

  const _handleFormProgressChange = (value: string) => {
    setFormProgressFilter(value);
  };

  const _handleBrandTitleChange = (value: string) => {
    setBrandTitleFilter(value);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-2">
        <label className="text-sm font-medium text-gray-900 dark:text-gray-300">Filtrar por Score</label>
        <div className="flex space-x-2">
          <Input
            type="number"
            placeholder="Min"
            value={scoreFilter.min}
            onChange={handleScoreMinChange}
            className="w-24"
          />
          <Input
            type="number"
            placeholder="Max"
            value={scoreFilter.max}
            onChange={handleScoreMaxChange}
            className="w-24"
          />
        </div>
      </div>
    </div>
  );
};

export default FilterComponent;
