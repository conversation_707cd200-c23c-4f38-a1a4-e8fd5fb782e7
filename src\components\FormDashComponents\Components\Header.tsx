// components/FormDashComponents/Header.tsx
import Spinner from "@/components/common/Spinner";
import React from "react";
import { FaSyncAlt } from "react-icons/fa";


interface HeaderProps {
  onManualUpdate: () => void;
  updating: boolean;
}

const Header: React.FC<HeaderProps> = ({ onManualUpdate, updating }) => {
  return (
    <div className="flex justify-end mb-4">
      <button
        onClick={onManualUpdate}
        disabled={updating}
        className="flex items-center px-4 py-2 bg-azul text-white rounded hover:bg-azul-escuro disabled:opacity-50"
      >
        {updating ? (
          <Spinner /> // Spinner durante a atualização
        ) : (
          <>
            <FaSyncAlt className="mr-2" />
            Atualizar
          </>
        )}
      </button>
    </div>
  );
};

export default Header;
