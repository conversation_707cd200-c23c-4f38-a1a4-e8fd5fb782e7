// frontend/components/FormDashComponents/Components/LeadCard.tsx
/* eslint-disable */

"use client";

import React from "react";
import { DelegateButton } from "./DelegateButton"; // Ajuste para o novo nome
import { RecoveryButton } from "./RecoveryButton"; // Se RecoveryButton for outro componente
import Tooltip from "./Tooltip";
import { Opportunity, StageHistory } from "@/types/crm";
import Link from "next/link";

interface LeadCardProps {
  lead: Opportunity;
  modalTitle: string;
}

export const LeadCard: React.FC<LeadCardProps> = ({ lead, modalTitle }) => {
  const [delegated, setDelegated] = React.useState(false);

  /**
   * Verifica se a oportunidade está no estágio de recuperação.
   * @param opp - A oportunidade a ser verificada.
   * @returns Boolean indicando se está em recuperação.
   */
  const isInRecovery = (opp: Opportunity): boolean => {
    return opp?.stageId === "506789";
  };

  /**
   * Verifica se a oportunidade passou pela recuperação e está no funil de vendas.
   * @param opp - A oportunidade a ser verificada.
   * @returns Boolean indicando se passou pela recuperação e está no funil de vendas.
   */
  const hasRecoveredAndInSales = (opp: Opportunity): boolean => {
    if (!opp) return false;

    const inSalesFunnel = opp.pipelineId === "56894";
    if (!inSalesFunnel) return false;

    let histories: StageHistory[] = [];

    if (opp.stageHistories) {
      if (typeof opp.stageHistories === "string") {
        try {
          histories = opp.stageHistories;
        } catch (error) {
          console.error("Erro ao fazer o parse do stageHistories:", error);
          histories = [];
        }
      } else {
        histories = opp.stageHistories; // Já é um objeto
      }
    }

    // Verifica se já passou pela etapa de recuperação (506789)
    return histories.some(
      (history: StageHistory) => String(history.in_stage_id) === "506789"
    );
  };

  /**
   * Obtém o score a partir do formData.
   * @returns Número do score ou null se não existir.
   */
  const getScoreFromFormData = (): number | null => {
    try {
      const formData: any = lead.formData
      return JSON.parse(formData)?.score || null; // Retorna o score do formData, se existir
    } catch (error) {
      console.error("Erro ao parsear formData:", error);
      return null;
    }
  };

  // Usa o score do lead ou o obtido do formData, caso o primeiro esteja vazio
  const leadScore = getScoreFromFormData();

  /**
   * Determina a classe de cor com base no score.
   * @param score - O score do lead.
   * @returns String com as classes CSS para a cor do score.
   */
  const getScoreColor = (score: number | null) => {
    if (score === null) {
      return "text-gray-600"; // Cor padrão se não houver score
    }
    if (score < 100) {
      return "text-red-600 font-bold"; // Score baixo
    } else if (score >= 100 && score <= 140) {
      return "text-orange-500 font-semibold"; // Score médio
    } else {
      return "text-green-600 font-medium"; // Score alto
    }
  };

  // Determina se o botão "Delegar" deve ser exibido
  const shouldShowDelegateButton =
    modalTitle.startsWith("Não Distribuído") &&
    lead?.stageId !== "506789" && // Form Incompleto
    lead?.stageId !== "488514"; // MQL ou outra etapa para MQL

  // Condições para exibir tags específicas
  const inRecovery = lead && isInRecovery(lead);
  const recoveredAndInSales = lead && hasRecoveredAndInSales(lead);

  return (
    !delegated && (
      <li key={lead.id} className="mb-4 border-b pb-2 dark:border-gray-600">
        <table className="min-w-full table-fixed border-collapse border border-gray-200 dark:border-gray-600">
          <thead>
            <tr className="bg-gray-50 dark:bg-gray-700">
              <th className="px-4 py-2 font-semibold w-[100px] truncate">ID</th>
              <th className="px-4 py-2 font-semibold w-[200px] truncate">
                Marca
              </th>
              <th className="px-4 py-2 font-semibold w-[80px] truncate">
                Score
              </th>
              {lead?.pipelineId === "56894" && (
                <>
                  <th className="px-4 py-2 font-semibold w-[150px] truncate">
                    Vendedor
                  </th>
                  <th className="px-4 py-2 font-semibold w-[150px] truncate">
                    Etapa Atual
                  </th>
                </>
              )}
              {(modalTitle.startsWith("Não Distribuído") ||
                recoveredAndInSales ||
                inRecovery) && (
                <th className="px-4 py-2 font-semibold w-[150px] truncate">
                  Ações
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            <tr>
              {/* ID */}
              <td className="px-4 py-2 w-[100px] truncate">
                <Link
                  href={`https://app.pipe.run/pipeline/gerenciador/visualizar/${lead.crmId}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline"
                >
                  {lead.crmId}
                </Link>
              </td>
              {/* Marca */}
              <td className="px-4 py-2 w-[200px] z-50">
                <Tooltip text={lead.brandTitle || "Nenhum dado encontrado"}>
                  <span className="font-bold block overflow-hidden w-[200px] truncate">
                    {lead.brandTitle}
                  </span>
                </Tooltip>
                {/* Tag Recuperado */}
                {recoveredAndInSales && (
                  <span className="inline-block px-2 py-1 mt-1 bg-green-100 text-green-800 text-xs font-semibold rounded dark:bg-green-900 dark:text-green-200">
                    Recuperado
                  </span>
                )}
              </td>
              {/* Score */}
              <td
                className={`px-4 py-2 w-[80px] truncate ${getScoreColor(
                  lead.score || null
                )}`}
              >
                {leadScore !== null ? leadScore : lead.score}
              </td>
              {/* Vendedor e Etapa Atual */}
              {lead?.pipelineId === "56894" && (
                <>
                  {/* Vendedor */}
                  <td className="px-4 py-2 w-[150px]">
                    <span className="truncate w-[150px] block overflow-hidden ">
                      {lead?.ownerName || "Não atribuído"}
                    </span>
                  </td>

                  {/* Etapa Atual */}
                  <td className="px-4 py-2 w-[150px]">
                    <span className="relative z-50 truncate w-[150px] block ">
                      {lead?.stageName || "Não atribuído"}
                    </span>
                  </td>
                </>
              )}

              {/* Ações */}
              {(modalTitle.startsWith("Não Distribuído") ||
                recoveredAndInSales ||
                inRecovery) && (
                <td className="px-4 py-2 w-[150px] truncate flex flex-col space-y-1">
                  {/* Botão Delegar */}
                  {shouldShowDelegateButton &&
                    !modalTitle.startsWith(
                      "Não Distribuído - Entrada - Form Incompleto"
                    ) && (
                      <DelegateButton
                        crmId={lead.crmId}
                        brandTitle={lead.brandTitle || "Nenhum dado encontrado"}
                        opportunity={lead} // Passe a oportunidade inteira
                        setDelegated={setDelegated}
                      />
                    )}

                  {/* Tag Recuperado */}
                  {recoveredAndInSales && (
                    <span className="inline-block px-2 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded dark:bg-green-900 dark:text-green-200">
                      Recuperado
                    </span>
                  )}
                  {/* Tag Em Recuperação */}
                  {inRecovery && (
                    <span className="inline-block px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-semibold rounded dark:bg-yellow-900 dark:text-yellow-200">
                      ⛑️ Recuperação
                    </span>
                  )}
                  {/* Botão RecoveryButton, se necessário */}
                  {modalTitle.startsWith(
                    "Não Distribuído - Entrada - Form Incompleto"
                  ) && <RecoveryButton crmId={lead.crmId} />}
                </td>
              )}
            </tr>
          </tbody>
        </table>
      </li>
    )
  );
};
