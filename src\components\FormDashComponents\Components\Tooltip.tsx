// components/Tooltip.tsx
import React, { useState, ReactNode } from "react";

interface TooltipProps {
  children: ReactNode;
  text: string;
}

const Tooltip: React.FC<TooltipProps> = ({ children, text }) => {
  const [showTooltip, setShowTooltip] = useState(false);

  return (
    <div
      className="relative"
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      {children}
      {showTooltip && (
        <div className="absolute left-0 bottom-full mb-2 bg-black text-white text-[0.95rem] p-1 rounded z-10">
          {text}
        </div>
      )}
    </div>
  );
};

export default Tooltip;
