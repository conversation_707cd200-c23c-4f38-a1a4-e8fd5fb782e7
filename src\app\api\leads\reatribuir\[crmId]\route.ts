import { NextResponse } from 'next/server';

const CRM_TOKEN = process.env.CRM_TOKEN;
const CRM_API_URL = 'https://api.pipe.run/v1';

export async function PUT(
  request: Request,
  { params }: { params: { crmId: string } }
) {
  try {
    const { crmId } = params;

    // Primeira requisição: atualiza o stage_id
    console.log('Enviando requisição para atualizar stage_id do lead:', crmId);
    const response = await fetch(`${CRM_API_URL}/deals/${crmId}`, {
      method: 'PUT',
      headers: {
        'accept': 'application/json',
        'content-type': 'application/json',
        'token': CRM_TOKEN || ''
      },
      body: JSON.stringify({ stage_id: 566243 })
    });
   // console.log('Resposta da API:', await response.clone().json());

    if (!response.ok) {
      throw new Error('Falha ao reatribuir lead');
    }

    // Segunda requisição: busca os dados atualizados do lead
    const updatedResponse = await fetch(`${CRM_API_URL}/deals/${crmId}`, {
      headers: {
        'accept': 'application/json',
        'token': CRM_TOKEN || ''
      }
    });

    if (!updatedResponse.ok) {
      throw new Error('Falha ao buscar dados atualizados do lead');
    }

    const updatedData = await updatedResponse.json();

    return NextResponse.json({
      success: true,
      data: updatedData
    });
  } catch (error) {
    console.error('Erro ao reatribuir lead:', error);
    return NextResponse.json(
      { success: false, error: 'Erro ao reatribuir lead' },
      { status: 500 }
    );
  }
} 