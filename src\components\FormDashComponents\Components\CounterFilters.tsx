import React from "react";

interface CounterProps {
  total: number;
  filtered: number;
}

const Counter: React.FC<CounterProps> = ({ total }) => {
  return (
    <div className="flex space-x-8">
      <div className="flex flex-col items-center bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <p className="text-sm font-medium text-gray-700 dark:text-gray-200">
          Total de Formulários
        </p>
        <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
          {total}
        </p>
      </div>
    </div>
  );
};

export default Counter;
