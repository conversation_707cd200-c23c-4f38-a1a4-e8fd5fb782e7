{"name": "form-dash", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.1.10", "@hello-pangea/dnd": "^17.0.0", "@heroicons/react": "^2.1.5", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@shadcn/ui": "^0.0.4", "@types/lodash": "^4.17.16", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-datepicker": "^6.2.0", "axios": "^1.7.7", "chart.js": "^4.4.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cookies-next": "^4.2.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "lodash": "^4.17.21", "lucide-react": "^0.445.0", "next": "^14.2.23", "react": "^18", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.2.0", "react-datepicker": "^7.6.0", "react-day-picker": "^9.5.0", "react-debounce-input": "^3.3.0", "react-dom": "^18", "react-icons": "^5.3.0", "react-toastify": "^10.0.6", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.8.1", "@typescript-eslint/parser": "^8.8.1", "eslint": "^8", "eslint-config-next": "14.2.13", "postcss": "^8", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}