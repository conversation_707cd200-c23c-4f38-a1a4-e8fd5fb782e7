import axios from 'axios';

const BASE_URL = 'https://api.pipe.run/v1';

// Função para obter o token do CRM
const getCrmToken = async () => {
  try {
    const response = await axios.get('/api/crm/token');
    return response.data.token;
  } catch (error) {
    console.error('Erro ao obter token do CRM:', error);
    throw new Error('Falha ao obter token do CRM');
  }
};

// Função para obter os detalhes de uma oportunidade
export const getOpportunityDetails = async (crmId: string) => {
  try {
    const token = await getCrmToken();
    const response = await axios.get(`${BASE_URL}/deals/${crmId}`, {
      headers: {
        accept: 'application/json',
        token
      }
    });
    return response.data;
  } catch (error) {
    console.error('Erro ao obter detalhes da oportunidade:', error);
    throw error;
  }
};

// Função para mover o lead para recuperação
export const moveLeadToRecovery = async (crmId: string) => {
  try {
    const response = await axios.get(
      `https://api.registrese.app.br/webhooks/crm/mover-lead/${crmId}`
    );
    return response.data;
  } catch (error) {
    console.error('Erro ao mover lead para recuperação:', error);
    throw error;
  }
};

export const getLeadsDistribuicao = async () => {
  try {
    const response = await fetch('https://api.pipe.run/v1/deals?deleted=false&stage_id=488514', {
      headers: {
        'token': process.env.NEXT_PUBLIC_CRM_TOKEN || '',
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('Falha ao buscar leads em distribuição');
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    console.error('Erro ao buscar leads em distribuição:', error);
    throw error;
  }
}; 