import { parseISO } from "date-fns";
import { toZonedTime } from "date-fns-tz";

export interface CustomFielMapContent {
  id: number;
  valor: any;
}

export interface CustomFieldsMap {
  [key: string]: CustomFielMapContent;
}

export const customFieldsMap: CustomFieldsMap = {
  entradaLead: { id: 593695, valor: null },
  horaEntradaLead: { id: 594787, valor: null },
  possuiLogotipo: { id: 193118, valor: null },
  email: { id: 240587, valor: null },
  dataInicio: { id: 557278, valor: null },
  estagioDoNegocio: { id: 213595, valor: null },
  canal: { id: 232453, valor: null },
  tipoDeNegocio: { id: 240584, valor: null },
  mobilePhone: { id: 244296, valor: null },
  quantosColaboradores: { id: 244308, valor: null },
  urlConversao: { id: 480567, valor: null },
  possuiCnpj: { id: 369377, valor: null },
  campanha: { id: 388863, valor: null },
  tipo: { id: 388865, valor: null },
  conteudo: { id: 388866, valor: null },
  keyword: { id: 388867, valor: null },
  origemCampanha: { id: 389520, valor: null },
  classes: { id: 194463, valor: null },
  indicadoPor: { id: 196895, valor: null },
  porte: { id: 594768, valor: null },
  Porte: { id: 213683, valor: null },
  comoFoiAtendido: { id: 228243, valor: null },
  formaDePagamentoOld: { id: 213678, valor: null },
  primeiroVencimentoPara: { id: 213680, valor: null },
  valorACobrar: { id: 213681, valor: null },
  formaDePagamento: { id: 464609, valor: null },
  parcelas: { id: 464610, valor: null },
  valorParcelas: { id: 466498, valor: null },
  valorEntrada: { id: 466497, valor: null },
  segundoVencimentoPara: { id: 464611, valor: null },
  protocolo: { id: 464564, valor: null },
  analiseCredito: { id: 512979, valor: null },
  viabilidadeRegistro: { id: 225839, valor: null },
  leadScore: { id: 204059, valor: null },
  negociacao: { id: 213682, valor: null },
  tipoLead: { id: 495556, valor: null },
  tokenSite: { id: 442905, valor: null },
  pTime: { id: 525692, valor: null },
  propostaNoQwril: { id: 213659, valor: null },
  idOriginal: { id: 549882, valor: null },
  faseDoPlanejamento: { id: 544091, valor: null },
  tempoParaResolver: { id: 554713, valor: null },
  investimentoNaMarca: { id: 554712, valor: null },
  ramoDeAtuacao: { id: 196382, valor: null },
  siteRedesSociais: { id: 203714, valor: null },
  horaVisita: { id: 594785, valor: null },
  horaConversao: { id: 594786, valor: null },
  plano: { id: 545920, valor: null },
  motivoRegistro: { id: 547311, valor: null },
  dispositivo: { id: 613811, valor: null },
  utmFinal: { id: 631782, valor: null },
  historicoCampanhas: { id: 631785, valor: null },
};
function formatarDataParaISO(data: string | null): string | null {
  if (!data) return null;

  try {
    // Remove espaços extras
    const dataLimpa = data.trim();

    // Caso 1: Data já está no formato ISO com timezone (2025-01-14T15:51:06-03:00)
    if (dataLimpa.includes("T") && dataLimpa.includes("-03:00")) {
      const dataObj = new Date(dataLimpa);
      const dataZoned = toZonedTime(dataObj, "America/Sao_Paulo");
      return dataZoned.toISOString();
    }

    // Caso 2: Data no formato DD/MM/YYYY, HH:mm:ss
    if (dataLimpa.includes("/") && dataLimpa.includes(",")) {
      const [dataParte, horaParte] = dataLimpa
        .split(",")
        .map((part) => part.trim());
      const [dia, mes, ano] = dataParte.split("/");
      const [hora, minuto, segundo] = horaParte.split(":");

      const dataObj = new Date(
        parseInt(ano),
        parseInt(mes) - 1,
        parseInt(dia),
        parseInt(hora),
        parseInt(minuto),
        parseInt(segundo) || 0
      );

      return dataObj.toISOString();
    }

    // Caso 3: Data no formato YYYY-MM-DD HH:mm:ss
    if (dataLimpa.includes("-") && dataLimpa.includes(":")) {
      const dataObj = parseISO(dataLimpa.replace(" ", "T"));
      return dataObj.toISOString();
    }

    // Se nenhum formato específico funcionar, tenta parse direto
    const dataObj = new Date(dataLimpa);
    if (!isNaN(dataObj.getTime())) {
      return dataObj.toISOString();
    }

    return null;
  } catch (error) {
    console.error(`Erro ao formatar data: ${data}`, error);
    return null;
  }
}
export function formatPhoneNumber(phoneNumber: string): string {
  // Verificar se o número de telefone é válido e se é uma string
  if (!phoneNumber || typeof phoneNumber !== "string") {
    return `Número de telefone inválido [${phoneNumber}]`;
  }

  // Remover o código do país (55) e o zero inicial, se existir
  let cleanedNumber = phoneNumber.replace(/^55/, "").replace(/^0/, "");

  // Verificar se o DDD é válido (dois primeiros dígitos após remoção do código do país)
  const ddd = cleanedNumber.substring(0, 2);
  if (!/^[1-9]{2}$/.test(ddd)) {
    return `Número de telefone inválido [${phoneNumber}]`;
  }

  // Extrair o número de telefone sem o DDD
  const numberWithoutDdd = cleanedNumber.substring(2);
  // Formatar o número final
  let formattedNumber;
  if (/^9\d{8}$/.test(numberWithoutDdd)) {
    // Quando o número começa com 9 e tem 9 dígitos
    formattedNumber = `(${ddd}) ${numberWithoutDdd.slice(
      0,
      5
    )}-${numberWithoutDdd.slice(5)}`;
  } else if (/^\d{8}$/.test(numberWithoutDdd)) {
    // Quando o número tem 8 dígitos
    formattedNumber = `(${ddd}) ${numberWithoutDdd.slice(
      0,
      4
    )}-${numberWithoutDdd.slice(4)}`;
  } else {
    return `Número de telefone inválido [${phoneNumber}]`;
  }

  return formattedNumber;
}
// Funções auxiliares
function processarDadosContato(person: any) {
  const leadContactPhone =
    Array.isArray(person?.contactPhones) && person.contactPhones.length > 0
      ? person.contactPhones[0]?.phone
      : null;

  return {
    telefone: leadContactPhone
      ? formatPhoneNumber(leadContactPhone)
      : null,
  };
}
export function processarCustomFields(customFields: any) {
  let localCustomFieldsMap: any = {};

  for (const key in customFieldsMap) {
    localCustomFieldsMap[key] = {
      id: customFieldsMap[key].id,
      valor: null,
    };
  }

  const customFieldsArray = Array.isArray(customFields) ? customFields : [];
  customFieldsArray.forEach((field: any) => {
    for (const key in localCustomFieldsMap) {
      if (localCustomFieldsMap[key].id === field.id) {
        let value = field.value;
        if (Array.isArray(value)) {
          value = value.join(", ");
        }
        localCustomFieldsMap[key].valor = value || " ";
        break;
      }
    }
  });

  return localCustomFieldsMap;
}
export function normalizeData(crmData: any[]): any[] {
  const startTime = new Date().toLocaleString("pt-BR", {
    timeZone: "America/Sao_Paulo",
  });
  console.log(
    `[${startTime}] Iniciando processamento de ${crmData.length} registros...`
  );

  const processedData = crmData.map((item) => {
    // Extrair dados principais
    const { customFields, person, stage, origin, city } = item;

    // Processar campos customizados
    const customFieldsProcessados = processarCustomFields(customFields);
    // Processar dados do contato
    const dadosContato = processarDadosContato(person);
    // Formatar datas
    const datasFormatadas = {
      created_at: formatarDataParaISO(item.created_at),
      closed_at: formatarDataParaISO(item.closed_at),
      horaVisita: formatarDataParaISO(
        customFieldsProcessados.horaVisita?.valor
      ),
      horaConversao: formatarDataParaISO(
        customFieldsProcessados.horaConversao?.valor
      ),
      horaEntradaLead: formatarDataParaISO(
        customFieldsProcessados.horaEntradaLead?.valor
      ),
      pTime: formatarDataParaISO(customFieldsProcessados.pTime?.valor),
    };

    return {
      // Dados básicos
      crmId: Number(item.id),
      etapa: stage?.name || null,
      stage_id: item.stage_id || null,
      ownerId: item.owner_id || null,
      ownerName: item.owner?.name || null,
      origem: origin?.name || null,
      cidade: city?.name || null,
      uf: city?.uf || null,

      // Datas (convertidas para DateTime)
      dataCadastro: datasFormatadas.created_at || null,
      entradaLead: datasFormatadas.horaEntradaLead || null,
      apresentacao: datasFormatadas.pTime || null,
      dataFechamento: datasFormatadas.closed_at || null,
      horaVisita: datasFormatadas.horaVisita || null,
      horaConversao: datasFormatadas.horaConversao || null,
      // Dados de contato
      fone: dadosContato.telefone,
      cpf: person?.cpf || null,
      nomeLead: person?.name || null,
      cnpj: item.company?.cnpj || null,
      
      // Dados da oportunidade (convertidos para tipos corretos)
      titulo: item.title || null,
      status: item.deleted === 1 ? 4 : Number(item.status) || 0,
      situacao: Number(item.freezed) || 0,
      deleted: item.deleted || null,
      valor: item.value ? parseFloat(item.value) : null,
      leadTime: item.lead_time ? item.lead_time : null,
      temLogotipo: customFieldsProcessados.possuiLogotipo?.valor || null,
      faseNegocio: customFieldsProcessados.estagioDoNegocio?.valor || null,
      canal: customFieldsProcessados.canal?.valor || null,
      tipoNegocio: customFieldsProcessados.tipoDeNegocio?.valor || null,
      colaboradores:
        customFieldsProcessados.quantosColaboradores?.valor || null,
      urlConversao: customFieldsProcessados.urlConversao?.valor || null,
      temCNPJ: String(customFieldsProcessados.possuiCnpj?.valor || ""),
      campanha: customFieldsProcessados.campanha?.valor || null,
      tipo: customFieldsProcessados.tipo?.valor || null,
      conteudo: customFieldsProcessados.conteudo?.valor || null,
      keyword: customFieldsProcessados.keyword?.valor || null,
      origemCampanha: customFieldsProcessados.origemCampanha?.valor || null,
      classes: customFieldsProcessados.classes?.valor || null,
      indicadoPor: customFieldsProcessados.indicadoPor?.valor || null,
      porte: customFieldsProcessados.Porte?.valor || null,
      comoFoiAtendido: customFieldsProcessados.comoFoiAtendido?.valor || null,
      motivoPerda: item.lost_reason_id ? Number(item.lost_reason_id) : null,
      formaPagamento:
        customFieldsProcessados.formaDePagamentoOld?.valor || null,
      primeiroVencimento:
        customFieldsProcessados.primeiroVencimentoPara?.valor || null,
      valorCobrar: customFieldsProcessados.valorACobrar?.valor || null,
      condicaoPagamento:
        customFieldsProcessados.formaDePagamento?.valor || null,
      parcelas: customFieldsProcessados.parcelas?.valor || null,
      valorParcela: customFieldsProcessados.valorParcelas?.valor || null,
      valorDemaisParcelas: customFieldsProcessados.valorEntrada?.valor || null,
      dataSegundoVencimento:
        customFieldsProcessados.segundoVencimentoPara?.valor || null,
      protocolo: customFieldsProcessados.protocolo?.valor || null,
      viabilidade: customFieldsProcessados.viabilidadeRegistro?.valor || null,
      score: customFieldsProcessados.leadScore?.valor || null,
      negociacao: customFieldsProcessados.negociacao?.valor || null,
      rangeScore: item.tags?.[0]?.name || null,
      tipoLead: customFieldsProcessados.tipoLead?.valor || null,
      tokenSite: customFieldsProcessados.tokenSite?.valor || null,
      pTime: customFieldsProcessados.pTime?.valor || null,
      proposta: customFieldsProcessados.propostaNoQwril?.valor || null,
      idOriginal: customFieldsProcessados.idOriginal?.valor || null,
      faseDoPlanejamento:
        customFieldsProcessados.faseDoPlanejamento?.valor || null,
      tempoResolver: customFieldsProcessados.tempoParaResolver?.valor || null,
      investiuMarca: customFieldsProcessados.investimentoNaMarca?.valor || null,
      ramoAtuacao: customFieldsProcessados.ramoDeAtuacao?.valor || null,
      redesSociaisSite: customFieldsProcessados.siteRedesSociais?.valor || null,
      tipoNegocioLead: customFieldsProcessados.tipoDeNegocio?.valor || null,
      analiseCredito: customFieldsProcessados.analiseCredito?.valor || null,
      plano: customFieldsProcessados.plano?.valor || null,
      motivoRegistro: customFieldsProcessados.motivoRegistro?.valor || null,
      dispositivo: customFieldsProcessados.dispositivo?.valor || null,
      utmFinal: customFieldsProcessados.utmFinal?.valor || null,
      historicoCampanhas:
        customFieldsProcessados.historicoCampanhas?.valor || null,
    };
  });

  const endTime = new Date().toLocaleString("pt-BR", {
    timeZone: "America/Sao_Paulo",
  });
  console.log(
    `[${endTime}] Processamento finalizado. Total de registros processados: ${processedData.length}`
  );

  return processedData;
}
