'use client';

import React, { useState, useMemo, useRef } from 'react';
import { Opportunity } from '@/types/crm';
import { FaStar, FaEye, FaPhone, FaClock, FaCalendarAlt, FaStopwatch, FaTrash, FaEllipsisV, FaUserPlus, FaEnvelope, FaUser, FaTag, FaTimesCircle, FaRandom } from 'react-icons/fa';
import { parseISO, format, differenceInMinutes } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useFormDataModalStore } from '@/context/useFormDataModalStore';
import Link from 'next/link';
import DeleteConfirmModal from './DeleteConfirmModal';
import Image from 'next/image';
import { Portal } from '@/components/Portal';
import { redistribuirLead } from '@/services/leadService';

interface KanbanCardProps {
  opportunity: Opportunity;
  currentVendedor: {
    ownerId: number;
    nome: string;
    avatar: string;
  };
  vendedores: {
    ownerId: number;
    nome: string;
    avatar: string;
  }[];
  onDelegate: (toVendedorId: number) => void;
  onDelete: () => void;
  onUpdate?: () => void;
  onRedistribuir?: (opportunity: Opportunity, currentVendedorId: number) => void;
}

// Função para calcular a cor do score
const getScoreColor = (score: number | undefined) => {
  if (score === undefined) return "text-gray-400";
  
  if (score <= 80) return "text-red-500";
  if (score <= 160) return "text-yellow-500";
  if (score <= 220) return "text-cyan-500";
  if (score <= 300) return "text-green-500";
  return "text-blue-500";
};

  // Função para definir a cor do ribbon com base no businessInternship
  const getRibbonColor = (businessInternship: string): string => {
    switch (businessInternship) {
      case "Já atuo há mais de 2 anos":
        return "#3B82F6"; // bg-blue-500
      case "Já atuo há mais de 6 meses":
        return "#07a8a8"; // cyan
      case "Comecei recentemente":
        return "#22C55E"; // verde do botão recuperar
      case "Pretendo começar em breve":
        return "#4B5563"; // cinza mais escuro
      case "Por enquanto é só um plano":
      default:
        return "#F3F4F6"; // cor do card
    }
  };

const formatDate = (date: string | null | undefined) => {
  if (!date) return '';
  try {
    return format(parseISO(date), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
  } catch (error) {
    return '';
  }
};

const KanbanCard: React.FC<KanbanCardProps> = ({
  opportunity,
  onDelete,
  vendedores,
  onDelegate,
  currentVendedor,
  onUpdate,
  onRedistribuir
}) => {
  const { openFormDataModal, setSelectedFormData, isFormDataModalOpen } = useFormDataModalStore();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const [showDelegateMenu, setShowDelegateMenu] = useState(false);
  const menuButtonRef = useRef<HTMLButtonElement>(null);

  const score = opportunity.formData?.score;
  const ribbonColor = getRibbonColor(opportunity.formData?.businessInternship || '');
  
  // Parse dos dados do formulário
  const parsedFormData = useMemo(() => {
    if (!opportunity || !opportunity.formData) return {};

    try {
      let formData = opportunity.formData;

      if (typeof formData === 'string') {
        formData = JSON.parse(formData);
      }

      return {
        ...formData,
        crmId: opportunity.crmId,
        inRecovery: opportunity.inRecovery || opportunity.stageId === "506789",
        stageId: opportunity.stageId,
        stageName: opportunity.stageName,
        brandTitle: opportunity.brandTitle,
        leadName: opportunity.leadName,
        leadNumber: opportunity.formData?.leadNumber,
        score: opportunity.formData?.score,
        origem: opportunity.formData?.origem,
        cidade: opportunity.formData?.cidade,
        uf: opportunity.formData?.uf,
        dataFechamento: opportunity.formData?.dataFechamento,
        horaVisita: opportunity.formData?.horaVisita,
        horaConversao: opportunity.formData?.horaConversao,
        cpf: opportunity.formData?.cpf,
        cnpj: opportunity.formData?.cnpj,
        status: opportunity.formData?.status,
        situacao: opportunity.formData?.situacao,
        valor: opportunity.formData?.valor,
        leadTime: opportunity.formData?.leadTime,
        temLogotipo: opportunity.formData?.temLogotipo,
        canal: opportunity.formData?.canal,
        tipoNegocio: opportunity.formData?.tipoNegocio,
        colaboradores: opportunity.formData?.colaboradores,
        urlConversao: opportunity.formData?.urlConversao,
        temCNPJ: opportunity.formData?.temCNPJ,
        campanha: opportunity.formData?.campanha,
        tipo: opportunity.formData?.tipo,
        conteudo: opportunity.formData?.conteudo,
        keyword: opportunity.formData?.keyword,
        origemCampanha: opportunity.formData?.origemCampanha,
        classes: opportunity.formData?.classes,
        indicadoPor: opportunity.formData?.indicadoPor,
        porte: opportunity.formData?.porte,
        comoFoiAtendido: opportunity.formData?.comoFoiAtendido,
        motivoPerda: opportunity.formData?.motivoPerda,
        formaPagamento: opportunity.formData?.formaPagamento,
        primeiroVencimento: opportunity.formData?.primeiroVencimento,
        valorCobrar: opportunity.formData?.valorCobrar,
        condicaoPagamento: opportunity.formData?.condicaoPagamento,
        parcelas: opportunity.formData?.parcelas,
        valorParcela: opportunity.formData?.valorParcela,
        valorDemaisParcelas: opportunity.formData?.valorDemaisParcelas,
        dataSegundoVencimento: opportunity.formData?.dataSegundoVencimento,
        protocolo: opportunity.formData?.protocolo,
        viabilidade: opportunity.formData?.viabilidade,
        rangeScore: opportunity.formData?.rangeScore,
        negociacao: opportunity.formData?.negociacao,
        tipoLead: opportunity.formData?.tipoLead,
        tokenSite: opportunity.formData?.tokenSite,
        pTime: opportunity.formData?.pTime,
        proposta: opportunity.formData?.proposta,
        idOriginal: opportunity.formData?.idOriginal,
        faseDoPlanejamento: opportunity.formData?.faseDoPlanejamento,
        tempoResolver: opportunity.formData?.tempoResolver,
        investiuMarca: opportunity.formData?.investiuMarca,
        ramoAtuacao: opportunity.formData?.ramoAtuacao,
        redesSociaisSite: opportunity.formData?.redesSociaisSite,
        tipoNegocioLead: opportunity.formData?.tipoNegocioLead,
        analiseCredito: opportunity.formData?.analiseCredito,
        plano: opportunity.formData?.plano,
        motivoRegistro: opportunity.formData?.motivoRegistro,
        dispositivo: opportunity.formData?.dispositivo,
        utmFinal: opportunity.formData?.utmFinal,
        historicoCampanhas: opportunity.formData?.historicoCampanhas
      };
    } catch (error) {
      console.error('Erro ao parsear formData:', error);
      return {};
    }
  }, [opportunity]);
  
  const handleViewDetails = () => {
    console.log(parsedFormData);
    setSelectedFormData(parsedFormData);
    openFormDataModal();
  };

  const handleDelete = async () => {
    try {
      const response = await fetch(
        `https://api.form-dash.registrese.app.br/opportunities/delete/${opportunity.crmId}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        onDelete?.();
        setShowDeleteModal(false);
      } else {
        console.error('Erro ao deletar oportunidade');
      }
    } catch (error) {
      console.error('Erro ao deletar oportunidade:', error);
    }
  };

  // Calcula o tempo de resposta
  const calcularTempoResposta = () => {
    const dataEntrada = opportunity.formData?.entradaLead || opportunity.formData?.dataCadastro;
    if (!dataEntrada || !opportunity.formData?.apresentacao) return null;
    
    const entradaLead = parseISO(dataEntrada);
    const apresentacao = parseISO(opportunity.formData.apresentacao);
    
    const minutos = differenceInMinutes(apresentacao, entradaLead);
    
    // Retorna null se o tempo for negativo
    if (minutos < 0) return null;
    
    if (minutos < 60) {
      return `${minutos} minutos`;
    } else {
      const horas = Math.floor(minutos / 60);
      const minutosRestantes = minutos % 60;
      return `${horas}h${minutosRestantes}min`;
    }
  };

  const tempoResposta = calcularTempoResposta();

  return (
    <>
      <div className={`relative flex flex-col justify-between p-3 ${
        opportunity.stageName === "📥 Entrada de leads" && !opportunity.formData?.apresentacao
          ? 'bg-yellow-50 dark:bg-yellow-900/20'
          : 'bg-white dark:bg-gray-800/80'
      } rounded-lg shadow-sm hover:shadow-md transition-all`}>
        {/* Status Badge */}
        {opportunity.formData?.status !== 0 && (
          <div
            className={
              opportunity.formData?.status === 1
                ? 'ribbon-ganho'
                : opportunity.formData?.status === 3
                ? 'ribbon-perdido'
                : ''
            }
          >
            <span>
              {opportunity.formData?.status === 1
                ? 'Ganho'
                : opportunity.formData?.status === 3
                ? 'Perdido'
                : ''}
            </span>
          </div>
        )}

        {opportunity.formData?.deleted && (
          <div className="absolute top-2 right-2 px-1.5 py-0.5 bg-red-500/90 text-white text-xs rounded-full">
            Deletado
          </div>
        )}
        
        {/* Faixa lateral colorida */}
        <div
          className="absolute top-0 left-0 h-full"
          style={{
            width: "4px",
            backgroundColor: ribbonColor,
            borderRadius: "6px 0 0 6px",
          }}
        ></div>

        {/* Conteúdo existente com padding ajustado para a faixa */}
        <div className="pl-2">
          {/* Cabeçalho com Marca e CRM ID */}
          <div className="mb-2">
            <div className="flex items-center justify-between mb-1">
              <h3 className="text-sm font-bold text-gray-900 dark:text-gray-100 uppercase truncate max-w-[70%]">
                {opportunity.brandTitle}
              </h3>
              <Link
                href={`https://app.pipe.run/pipeline/gerenciador/visualizar/${opportunity.crmId}`}
                target="_blank"
                className="text-xs text-blue-500 dark:text-blue-400 hover:text-blue-700"
              >
                #{opportunity.crmId}
              </Link>
            </div>

            {/* Score */}
            <div className="flex items-center justify-between">
              <div className={`flex items-center ${getScoreColor(score)} text-base font-semibold`}>
                <FaStar className="mr-1" size={14} />
                {score || 0}
              </div>
              <span className="text-xs font-medium px-1.5 py-0.5 rounded-full bg-gray-100 dark:bg-gray-700/80 text-gray-700 dark:text-gray-300">
                {opportunity.stageName}
              </span>
            </div>
          </div>

          {/* Informações de Data e Contato */}
          <div className="space-y-1.5">
            {/* Data de Entrada */}
            <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
              <FaCalendarAlt className="mr-1.5 shrink-0" size={12} />
              <span className="truncate">Entrada: {formatDate(opportunity.formData?.entradaLead) || "DC. " + formatDate(opportunity.formData?.dataCadastro)}</span>
            </div>

            {/* Apresentação */}
            <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
              <FaStopwatch className="mr-1.5 shrink-0" size={12} />
              <span className="truncate">Apresentação: {formatDate(opportunity.formData?.apresentacao)}</span>
            </div>

            {/* Tempo de Resposta */}
            {tempoResposta && (
              <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                <FaClock className="mr-1.5 shrink-0" size={12} />
                <span className="truncate">Tempo: {tempoResposta}</span>
              </div>
            )}

            {/* Telefone */}
         {
          /*
          <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
          <FaPhone className="mr-1.5 shrink-0" size={12} />
          <span className="truncate">{opportunity.formData?.leadNumber}</span>
          </div>
          */
        }
        </div>

          {/* Rodapé */}
          <div className="mt-3 pt-2 border-t border-gray-100 dark:border-gray-700/50 flex justify-between items-center">
            <button
              onClick={() => setShowDeleteModal(true)}
              className="text-red-400 hover:text-red-500 dark:text-red-400 dark:hover:text-red-300 transition-colors p-1"
              aria-label="Deletar"
            >
              <FaTrash size={14} />
            </button>

            <div className="flex items-center gap-2">
              <button
                onClick={handleViewDetails}
                className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 transition-colors p-1"
                aria-label="Ver detalhes"
              >
                <FaEye size={16} />
              </button>

              <div className="relative">
                <button
                  ref={menuButtonRef}
                  onClick={() => {
                    setShowMenu(!showMenu);
                    setShowDelegateMenu(false);
                  }}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 transition-colors p-1"
                  aria-label="Mais opções"
                >
                  <FaEllipsisV size={14} />
                </button>

                {/* Menu de Contexto */}
                {showMenu && menuButtonRef.current && (
                  <Portal>
                    <div 
                      className="fixed bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 min-w-[160px] z-[60]"
                      style={{
                        top: `${menuButtonRef.current.getBoundingClientRect().top - 40}px`,
                        left: `${menuButtonRef.current.getBoundingClientRect().left - 160}px`
                      }}
                    >
                      <div 
                        className="relative px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-sm text-gray-700 dark:text-gray-300 flex items-center justify-between group"
                        onMouseEnter={() => setShowDelegateMenu(true)}
                      >
                        <div className="flex items-center gap-2">
                          <FaUserPlus size={14} />
                          <span>Delegar para</span>
                        </div>
                        <span className="text-gray-400">›</span>

                        {/* Submenu de Delegação */}
                        {showDelegateMenu && (
                          <div 
                            className="absolute left-full top-0 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 min-w-[200px] -mt-2 -mr-2 z-[61] max-h-[300px] overflow-y-auto"
                          >
                            {/* Área invisível para manter o hover */}
                            <div 
                              className="absolute right-full top-0 w-4 h-full"
                              onMouseEnter={() => setShowDelegateMenu(true)}
                            />

                            {vendedores
                              .filter(v => v.ownerId !== currentVendedor.ownerId)
                              .map(vendedor => (
                                <button
                                  key={vendedor.ownerId}
                                  onClick={() => {
                                    onDelegate(vendedor.ownerId);
                                    setShowMenu(false);
                                    setShowDelegateMenu(false);
                                  }}
                                  className="w-full px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                                >
                                  <div className="relative w-6 h-6 rounded-full overflow-hidden">
                                    <Image
                                      src={vendedor.avatar || '/default-avatar.png'}
                                      alt={vendedor.nome}
                                      fill
                                      sizes="24px"
                                      className="object-cover"
                                    />
                                  </div>
                                  <span className="text-sm text-gray-700 dark:text-gray-300 truncate">
                                    {vendedor.nome}
                                  </span>
                                </button>
                              ))}
                          </div>
                        )}
                      </div>

                      {/* Novo item: Redistribuir Lead */}
                      <div
                        className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer text-sm text-gray-700 dark:text-gray-300 flex items-center gap-2"
                        onClick={() => {
                          setShowMenu(false);
                          if (onRedistribuir) {
                            onRedistribuir(opportunity, currentVendedor.ownerId);
                          } else {
                            // Fallback para a função antiga se a nova não estiver disponível
                            redistribuirLead(opportunity.crmId)
                              .then((result) => {
                                if (result.success && onUpdate) {
                                  onUpdate();
                                }
                              })
                              .catch((error) => {
                                console.error('Erro ao redistribuir lead:', error);
                              });
                          }
                        }}
                      >
                        <FaRandom size={14} />
                        <span>Redistribuir</span>
                      </div>
                    </div>

                    {/* Overlay para fechar os menus quando clicar fora */}
                    <div 
                      className="fixed inset-0 z-[59]"
                      onClick={() => {
                        setShowMenu(false);
                        setShowDelegateMenu(false);
                      }}
                    />
                  </Portal>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modal de confirmação de deleção */}
      <DeleteConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDelete}
        title="Confirmar Deleção"
        message={`Tem certeza que deseja deletar a oportunidade #${opportunity.crmId}?`}
      />
    </>
  );
};

export default KanbanCard; 
