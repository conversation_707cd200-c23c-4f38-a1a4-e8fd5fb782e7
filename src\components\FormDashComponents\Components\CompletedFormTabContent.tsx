import React, { useState, useMemo, useEffect, useCallback } from "react";
import axios from "axios";
import { Opportunity } from "@/types/crm";
import FormDataModal from "./FormDataModal";
import FullCalendar from "./FullCalendar";
import ListModalContent from "./ListModalContent";
import SideModal from "./SideModal";
import { isSameDay, format, startOfMonth } from "date-fns";
import { FaCalendarAlt } from "react-icons/fa";
import { groupFormsByDate } from "../DashUtilsTs/DatesUtils";
import { useCalendarStore } from "@/context/useReactStatesStore";
import { useFormDataModalStore } from "@/context/useFormDataModalStore";
import { DateRange } from "react-day-picker";

const CompletedFormTabContent: React.FC = () => {
  const {
    currentMonth,
    selectedDate,
    isListModalOpen,
    toggleListModal,
    isCalendarMinimized,
    setIsCalendarMinimized,
    setSelectedDate,
    setFormsGroupedByDate
  } = useCalendarStore();

  const { isFormDataModalOpen } = useFormDataModalStore();
  const [completedForms, setCompletedForms] = useState<Opportunity[]>([]);
  const [dateRange, setDateRange] = useState<DateRange | undefined>();

  // Estados para filtros
  const [scoreFilter, setScoreFilter] = useState<{
    min: number | "";
    max: number | "";
  }>({ min: "", max: "" });
  const [formProgressFilter, setFormProgressFilter] = useState("");
  const [brandTitleFilter, setBrandTitleFilter] = useState("");

  // Efeito para controlar a minimização do calendário baseado no tamanho da tela
  useEffect(() => {
    const handleResize = () => {
      setIsCalendarMinimized(window.innerWidth <= 1500 && isListModalOpen);
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isListModalOpen, setIsCalendarMinimized]);

  // Função para buscar oportunidades completas da API
  const fetchCompletedOpportunities = useCallback(async () => {
    const createdAtStart = format(startOfMonth(currentMonth), "yyyy-MM-dd");
    const endpoint = `https://api.form-dash.registrese.app.br/opportunities/getCompletedOpportunities?created_at_start=${createdAtStart}`;

    try {
      const response = await axios.get<{
        success: boolean;
        data: Record<string, Opportunity[]>;
      }>(endpoint);
      const data = response.data.data;
      
      const parsedForms = Object.entries(data).flatMap(([_date, forms]) =>
        (Array.isArray(forms) ? forms : []).map((form) => ({
          ...form,
          createdAt: form.createdAt,
          formData:
            typeof form.formData === "string"
              ? JSON.parse(form.formData)
              : form.formData,
        }))
      );
      
      setCompletedForms(parsedForms);
      
      // Agrupar os formulários por data e atualizar o store
      const groupedForms = groupFormsByDate(parsedForms, currentMonth);
      setFormsGroupedByDate(groupedForms);
      
    } catch (error) {
      console.error("Erro ao buscar oportunidades completas:", error);
    }
  }, [currentMonth, setFormsGroupedByDate]);

  // Buscar oportunidades quando o componente for montado ou o mês mudar
  useEffect(() => {
    fetchCompletedOpportunities();
  }, [fetchCompletedOpportunities]);

  // Obter valores únicos para formProgress e brandTitle
  const uniqueFormProgressValues = useMemo(() => {
    const progressSet = new Set<string>();
    completedForms.forEach((opp) => {
      if (opp.formProgress) progressSet.add(opp.formProgress);
    });
    return Array.from(progressSet);
  }, [completedForms]);

  const uniqueBrandTitles = useMemo(() => {
    const brandSet = new Set<string>();
    completedForms.forEach((opp) => {
      if (opp.brandTitle) brandSet.add(opp.brandTitle);
    });
    return Array.from(brandSet);
  }, [completedForms]);

  // Aplicar filtros
  const filteredFormsForDate = useMemo(() => {
    if (!selectedDate) return [];

    const formsForSelectedDate = completedForms.filter((opp) =>
      isSameDay(new Date(opp.createdAt), selectedDate)
    );

    let filteredForms = [...formsForSelectedDate];

    if (brandTitleFilter) {
      filteredForms = filteredForms.filter((opp) =>
        opp.brandTitle?.toLowerCase().includes(brandTitleFilter.toLowerCase())
      );
    }

    if (scoreFilter.min !== "" || scoreFilter.max !== "") {
      filteredForms = filteredForms.filter((opp) => {
        const score =
          typeof opp.formData === "string"
            ? JSON.parse(opp.formData)?.score
            : opp.formData?.score;
        return (
          (scoreFilter.min === "" || score >= Number(scoreFilter.min)) &&
          (scoreFilter.max === "" || score <= Number(scoreFilter.max))
        );
      });
    }

    if (formProgressFilter) {
      filteredForms = filteredForms.filter(
        (opp) => opp.formProgress === formProgressFilter
      );
    }

    return filteredForms;
  }, [
    completedForms,
    selectedDate,
    brandTitleFilter,
    scoreFilter,
    formProgressFilter,
  ]);

  return (
    <div className="flex flex-row h-full justify-between">
      {/* Calendário */}
      <div className={`transition-all duration-300 ease-in-out p-4 bg-gray-50 dark:bg-gray-800 rounded-lg shadow-lg w-full`}>
        <div className="flex flex-col md:flex-row items-center justify-between mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200">
            Forms Completos <FaCalendarAlt className="inline ml-2" />
          </h2>
        </div>
        <div className={`transition-all duration-300 ease-in-out p-4 bg-gray-50 dark:bg-gray-800 rounded-lg shadow-lg ${
          isCalendarMinimized ? "min-w-2/3" : "w-full"
        }`}>
          <FullCalendar />
        </div>
      </div>

      {/* SideModal para listar os formulários */}
      {isListModalOpen && (
        <div className="transition-all duration-300 ease-in-out bg-white dark:bg-gray-900 shadow-lg w-1/3">
          <SideModal
            isOpen={isListModalOpen}
            onClose={() => {
              toggleListModal();
              if (window.innerWidth <= 1500) {
                setIsCalendarMinimized(false);
              }
            }}
            header={<h2 className="text-xl font-semibold">Formulários Completos</h2>}
          >
            <ListModalContent
              _totalForms={completedForms.length}
              filteredForms={filteredFormsForDate}
              scoreFilter={scoreFilter}
              setScoreFilter={setScoreFilter}
              formProgressFilter={formProgressFilter}
              setFormProgressFilter={setFormProgressFilter}
              uniqueFormProgressValues={uniqueFormProgressValues}
              uniqueBrandTitles={uniqueBrandTitles}
              brandTitleFilter={brandTitleFilter}
              setBrandTitleFilter={setBrandTitleFilter}
              onUpdate={fetchCompletedOpportunities}
              _isOpenFormDataModal={isFormDataModalOpen}
              selectedDate={selectedDate || new Date()}
              onDateChange={(date) => {
                setSelectedDate(date);
                setDateRange(undefined);
                if (!isListModalOpen) {
                  toggleListModal();
                }
              }}
              dateRange={dateRange}
              onDateRangeChange={(range) => {
                setDateRange(range);
                if (range?.from) {
                  setSelectedDate(range.from);
                }
                if (!isListModalOpen) {
                  toggleListModal();
                }
              }}
            />
          </SideModal>
        </div>
      )}

      {/* FormDataModal para exibir os detalhes */}
      {isFormDataModalOpen && (
        <div className="transition-all duration-300 ease-in-out bg-white dark:bg-gray-900 shadow-lg w-1/2 h-full overflow-auto">
          <FormDataModal />
        </div>
      )}
    </div>
  );
};

export default CompletedFormTabContent; 