import {
  startOfMonth,
  endOfMonth,
  startOfWeek,
  endOfWeek,
  addDays,
  isSameMonth,
  isSameDay,
  format,
  eachDayOfInterval,
  isWithinInterval,
} from "date-fns";
import { Opportunity } from "@/types/crm";
import React from "react";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";

export const getCalendarCells = ({
  currentMonth,
  selectedDate,
  setSelectedDate,
  toggleListModal,
  formsGroupedByDate,
}: {
  currentMonth: Date;
  selectedDate: Date | null;
  setSelectedDate: (date: Date) => void;
  toggleListModal: () => void;
  formsGroupedByDate: Record<string, Opportunity[]>;
}) => {
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(monthStart);
  const startDate = startOfWeek(monthStart, { weekStartsOn: 0 });
  const endDate = endOfWeek(monthEnd, { weekStartsOn: 0 });

  const days: React.ReactNode[] = [];
  let day = startDate;
  const dateFormat = "d";

  while (day <= endDate) {
    const cloneDay = day;
    const formattedDate = format(day, dateFormat);
    const dateKey = format(cloneDay, "yyyy-MM-dd");
    const hasForms =
      formsGroupedByDate[dateKey] && formsGroupedByDate[dateKey].length > 0;

    days.push(
      <Tooltip key={day.toString()}>
        <TooltipTrigger>
          <div
            className={`w-full h-20 p-2 border rounded-md cursor-pointer flex flex-col items-center justify-center relative transition-all duration-200 ease-in-out ${
              !isSameMonth(day, monthStart)
                ? "text-gray-400 bg-gray-50 dark:bg-gray-800"
                : isSameDay(day, selectedDate || new Date())
                ? "bg-blue-500 text-white shadow-lg dark:shadow-md rounded-md"
                : "hover:shadow-lg hover:bg-white dark:hover:bg-gray-700 dark:text-gray-700 dark:hover:text-gray-50"
            }`}
            onClick={() => {
              setSelectedDate(cloneDay);
              if (hasForms) {
                toggleListModal();
              }
            }}
          >
            <span className="text-sm font-semibold">{formattedDate}</span>
            {hasForms && (
              <Badge className="absolute top-2 right-2 bg-red-500 text-white text-xs">
                {formsGroupedByDate[dateKey].length}
              </Badge>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          {hasForms ? "Há formulários neste dia" : "Sem formulários"}
        </TooltipContent>
      </Tooltip>
    );

    day = addDays(day, 1);
  }

  return days;
};

export const groupFormsByDate = (forms: Opportunity[], currentMonth: Date) => {
  const start = startOfMonth(currentMonth);
  const end = endOfMonth(currentMonth);
  const daysInMonth = eachDayOfInterval({ start, end });

  const formsByDate = daysInMonth.reduce((acc, date) => {
    const formsForDate = forms.filter((form) =>
      isWithinInterval(new Date(form.createdAt), { start, end }) &&
      isSameDay(new Date(form.createdAt), date)
    );
    acc[format(date, "yyyy-MM-dd")] = formsForDate;
    return acc;
  }, {} as Record<string, Opportunity[]>);

  return formsByDate;
};