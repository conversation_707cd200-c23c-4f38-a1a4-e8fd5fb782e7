import React, { useState, useEffect, useRef, useMemo } from 'react';
import { FaFilter, FaTimes } from 'react-icons/fa';
import { Opportunity } from '@/types/crm';
import { Dispatch, SetStateAction } from 'react';
import { startOfDay, endOfDay, parseISO } from 'date-fns';

interface FilterState {
  brandTitle: string;
  crmId: string;
  stage: string;
  scoreMin: string;
  scoreMax: string;
  date: string;
}

interface Vendedor {
  ownerId: number;
  nome: string;
  avatar: string;
  opportunities: Opportunity[];
  filteredOpportunities: Opportunity[] | null;
  currentPage: number;
  totalPages: number;
  isLoadingMore: boolean;
  dateStart: string;
}

interface VendedorFilterProps {
  opportunities: Opportunity[];
  onFilter: (filteredOpportunities: Opportunity[]) => void;
  ownerId: number;
  onDateChange: (date: string) => Promise<void>;
}

const VendedorFilter: React.FC<VendedorFilterProps> = ({ 
  opportunities, 
  onFilter,
  ownerId,
  onDateChange
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    brandTitle: '',
    crmId: '',
    stage: '',
    scoreMin: '',
    scoreMax: '',
    date: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const filterRef = useRef<HTMLDivElement>(null);

  // Lista única de etapas disponíveis
  const stages = useMemo(() => {
    const allStages = opportunities
      .map(opp => opp.stageName)
      .filter((stage): stage is string => typeof stage === 'string' && stage.length > 0);
    
    return Array.from(new Set(allStages));
  }, [opportunities]);

  // Efeito para fechar o filtro quando clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Aplica os filtros
  const applyFilters = () => {
    const filtered = opportunities.filter(opp => {
      const matchBrandTitle = filters.brandTitle
        ? opp.brandTitle?.toLowerCase().includes(filters.brandTitle.toLowerCase())
        : true;

      const matchCrmId = filters.crmId
        ? opp.crmId.toString().includes(filters.crmId)
        : true;

      const matchStage = filters.stage
        ? opp.stageName === filters.stage
        : true;

      const score = Number(opp.formData?.score);
      const scoreMin = filters.scoreMin ? Number(filters.scoreMin) : -Infinity;
      const scoreMax = filters.scoreMax ? Number(filters.scoreMax) : Infinity;
      const matchScore = !isNaN(score) && score >= scoreMin && score <= scoreMax;

      // Filtro por data específica
      if (filters.date) {
        const oppDate = opp.formData?.entradaLead 
          ? new Date(opp.formData.entradaLead)
          : new Date(opp.createdAt);

        const selectedDate = parseISO(filters.date);
        const startOfSelectedDay = startOfDay(selectedDate);
        const endOfSelectedDay = endOfDay(selectedDate);

        return oppDate >= startOfSelectedDay && oppDate <= endOfSelectedDay;
      }

      return matchBrandTitle && matchCrmId && matchStage && matchScore;
    });

    onFilter(filtered);
  };

  // Limpa todos os filtros
  const clearFilters = () => {
    setFilters({
      brandTitle: '',
      crmId: '',
      stage: '',
      scoreMin: '',
      scoreMax: '',
      date: ''
    });
    onFilter(opportunities);
  };

  // Atualiza os filtros e aplica
  const handleFilterChange = async (field: keyof FilterState, value: string) => {
    setFilters(prev => ({ ...prev, [field]: value }));

    // Se for alteração de data, busca na API
    if (field === 'date') {
      setIsLoading(true);
      try {
        await onDateChange(value);
      } catch (error) {
        console.error('Erro ao buscar dados por data:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    applyFilters();
  }, [filters]);

  const hasActiveFilters = Object.values(filters).some(value => value !== '');

  return (
    <div className="relative" ref={filterRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`p-1.5 rounded-md transition-colors ${
          hasActiveFilters
            ? 'bg-blue-500 text-white hover:bg-blue-600'
            : 'text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700'
        }`}
        title="Filtrar leads"
      >
        <FaFilter size={14} />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-72 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50">
          <div className="p-4 space-y-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                Filtros
              </h3>
              {hasActiveFilters && (
                <button
                  onClick={clearFilters}
                  className="text-xs text-red-500 hover:text-red-600 flex items-center gap-1"
                >
                  <FaTimes size={12} />
                  Limpar filtros
                </button>
              )}
            </div>

            {/* Filtro por Data */}
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Filtrar por dia específico
              </label>
              <div className="relative">
                <input
                  type="date"
                  value={filters.date}
                  onChange={(e) => handleFilterChange('date', e.target.value)}
                  className="w-full px-3 py-1.5 text-sm rounded-md border border-gray-300 dark:border-gray-600 
                           bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                  disabled={isLoading}
                />
                {isLoading && (
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
                  </div>
                )}
              </div>
            </div>

            {/* Filtro por Marca */}
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Nome da Marca
              </label>
              <input
                type="text"
                value={filters.brandTitle}
                onChange={(e) => handleFilterChange('brandTitle', e.target.value)}
                placeholder="Digite o nome da marca"
                className="w-full px-3 py-1.5 text-sm rounded-md border border-gray-300 dark:border-gray-600 
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>

            {/* Filtro por CRM ID */}
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                ID no CRM
              </label>
              <input
                type="text"
                value={filters.crmId}
                onChange={(e) => handleFilterChange('crmId', e.target.value)}
                placeholder="Digite o ID"
                className="w-full px-3 py-1.5 text-sm rounded-md border border-gray-300 dark:border-gray-600 
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>

            {/* Filtro por Etapa */}
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Etapa
              </label>
              <select
                value={filters.stage}
                onChange={(e) => handleFilterChange('stage', e.target.value)}
                className="w-full px-3 py-1.5 text-sm rounded-md border border-gray-300 dark:border-gray-600 
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                <option value="">Todas as etapas</option>
                {stages.map((stage) => (
                  <option key={stage} value={stage}>
                    {stage}
                  </option>
                ))}
              </select>
            </div>

            {/* Filtro por Score (Intervalo) */}
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Score (Intervalo)
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="number"
                  value={filters.scoreMin}
                  onChange={(e) => handleFilterChange('scoreMin', e.target.value)}
                  placeholder="Min"
                  className="w-full px-3 py-1.5 text-sm rounded-md border border-gray-300 dark:border-gray-600 
                           bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                />
                <span className="text-gray-500 dark:text-gray-400">até</span>
                <input
                  type="number"
                  value={filters.scoreMax}
                  onChange={(e) => handleFilterChange('scoreMax', e.target.value)}
                  placeholder="Max"
                  className="w-full px-3 py-1.5 text-sm rounded-md border border-gray-300 dark:border-gray-600 
                           bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VendedorFilter; 