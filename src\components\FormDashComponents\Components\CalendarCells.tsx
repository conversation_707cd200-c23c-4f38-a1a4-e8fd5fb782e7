// src/components/CalendarCells.tsx
import React from "react";
import { Opportunity } from "@/types/crm";
import { getCalendarCells } from "../DashUtilsTs/DatesUtils";

interface CalendarCellsProps {
  currentMonth: Date;
  selectedDate: Date | null;
  setSelectedDate: (date: Date) => void;
  toggleListModal: () => void;
  formsGroupedByDate: Record<string, Opportunity[]>;
}

const CalendarCells: React.FC<CalendarCellsProps> = ({
  currentMonth,
  selectedDate,
  setSelectedDate,
  toggleListModal,
  formsGroupedByDate,
}) => {
  const cells = getCalendarCells({
    currentMonth,
    selectedDate,
    setSelectedDate,
    toggleListModal,
    formsGroupedByDate,
  });

  return <div className="grid grid-cols-7 gap-2">{cells}</div>;
};

export default CalendarCells;
