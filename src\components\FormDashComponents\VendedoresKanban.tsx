'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import KanbanCard from './KanbanCard';
import { Opportunity } from '@/types/crm';
import { DragDropContext, Draggable, DropResult, Droppable } from '@hello-pangea/dnd';
import Image from 'next/image';
import { toast } from 'react-toastify';
import { format, subDays, parseISO, startOfDay, endOfDay } from 'date-fns';
import { cacheService } from '@/services/cacheService';
import VendedorFormDataModal from './Components/VendedorFormDataModal';
import { useFormDataModalStore } from '@/context/useFormDataModalStore';
import { FaSync } from 'react-icons/fa';
import DelegateConfirmModal from './Components/DelegateConfirmModal';
import { ptBR } from 'date-fns/locale';
import VendedorFilter from './Components/VendedorFilter';
import DateFilter from './Components/DateFilter';
import LeadsDistribuicaoNotification from './LeadsDistribuicaoNotification';

interface Vendedor {
  ownerId: number;
  nome: string;
  avatar: string;
  opportunities: Opportunity[];
  filteredOpportunities: Opportunity[] | null;
  currentPage: string;
  totalPages: string;
  isLoadingMore: boolean;
  dateStart: string;
}

interface OpportunitiesResult {
  opportunities: Opportunity[];
  pagination: {
    total: string;
    count: string;
    perPage: string;
    currentPage: string;
    totalPages: string;
    hasNextPage: boolean;
    dateStart: string;
    dateEnd: string;
  };
}

interface DateRange {
  start: string;
  end: string;
}

const INITIAL_DAYS = 0; // Busca inicial apenas do dia atual
const LOAD_MORE_DAYS = 1; // Carrega mais 1 dia quando chegar ao fim
const CACHE_EXPIRATION = 5 * 60 * 1000; // 5 minutos em milissegundos
const BACKGROUND_REFRESH_INTERVAL = 2 * 60 * 1000; // 2 minutos em milissegundos

const sortOpportunities = (opportunities: Opportunity[]) => {
  return [...opportunities].sort((a, b) => {
    const aDate = a.formData?.entradaLead 
      ? new Date(a.formData.entradaLead).getTime()
      : new Date(a.createdAt).getTime();
    
    const bDate = b.formData?.entradaLead 
      ? new Date(b.formData.entradaLead).getTime()
      : new Date(b.createdAt).getTime();

    // Ordenação decrescente (mais recente primeiro)
    return bDate - aDate;
  });
};

// Função para limpar cache antigo
const cleanOldCache = () => {
  try {
    const now = new Date().getTime();
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith('opportunity_')) {
        const item = localStorage.getItem(key);
        if (item) {
          const { timestamp } = JSON.parse(item);
          if (now - timestamp > CACHE_EXPIRATION) {
            localStorage.removeItem(key);
          }
        }
      }
    }
  } catch (error) {
    console.error('Erro ao limpar cache:', error);
    // Em caso de erro, limpa todo o cache relacionado a oportunidades
    Object.keys(localStorage)
      .filter(key => key.startsWith('opportunity_'))
      .forEach(key => localStorage.removeItem(key));
  }
};

const VendedoresKanban: React.FC = () => {
  const [vendedores, setVendedores] = useState<Vendedor[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedDate, setSelectedDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [dateRange, setDateRange] = useState<DateRange>({
    start: format(startOfDay(new Date()), 'yyyy-MM-dd'),
    end: format(endOfDay(new Date()), 'yyyy-MM-dd')
  });
  const fetchControlRef = useRef<{ [key: string]: boolean }>({});
  const { isFormDataModalOpen, selectedFormData, closeFormDataModal } = useFormDataModalStore();
  const [delegateConfirm, setDelegateConfirm] = useState<{
    isOpen: boolean;
    opportunity: Opportunity | null;
    fromVendedor: { ownerId: number; nome: string } | null;
    toVendedor: { ownerId: number; nome: string } | null;
  }>({
    isOpen: false,
    opportunity: null,
    fromVendedor: null,
    toVendedor: null
  });

  // Limpa o cache antigo quando o componente é montado
  useEffect(() => {
    cleanOldCache();
  }, []);

  // Função para buscar oportunidades de um vendedor
  const fetchOpportunities = useCallback(async (
    ownerId: number,
    dateStart: string,
    dateEnd: string,
    page: string = '1',
    bypassCache: boolean = false
  ): Promise<OpportunitiesResult | null> => {
    const fetchKey = `opportunities_${ownerId}-${dateStart}-${dateEnd}-${page}`;
    
    if (fetchControlRef.current[fetchKey]) {
      return null;
    }

    try {
      fetchControlRef.current[fetchKey] = true;

      const daysAgo = Math.floor(
        (new Date().getTime() - new Date(dateStart).getTime()) / (1000 * 60 * 60 * 24)
      );

      const shouldUseCache = daysAgo > 7 && !bypassCache;

      if (shouldUseCache) {
        const cachedData = cacheService.get<OpportunitiesResult>(fetchKey);
        if (cachedData) {
          return cachedData;
        }
      }

      const response = await fetch(
        `/api/vendedores/${ownerId}/opportunities?page=${page}&dateStart=${dateStart}&dateEnd=${dateEnd}`
      );
      const data = await response.json();

      if (data.success) {
        const result: OpportunitiesResult = {
          opportunities: data.data,
          pagination: data.pagination
        };

        if (shouldUseCache) {
          cacheService.set(fetchKey, result, CACHE_EXPIRATION);
        }

        return result;
      } else {
        throw new Error(data.error);
      }
    } catch (error) {
      console.error('Erro ao buscar oportunidades:', error);
      if (!bypassCache) {
        toast.error('Erro ao carregar oportunidades');
      }
      return null;
    } finally {
      delete fetchControlRef.current[fetchKey];
    }
  }, []);

  // Atualiza o intervalo de atualização automática para ser mais frequente para dados recentes
  useEffect(() => {
    const updateIntervals: { [key: number]: NodeJS.Timeout } = {};

    vendedores.forEach((vendedor, index) => {
      const daysAgo = Math.floor(
        (new Date().getTime() - new Date(vendedor.dateStart).getTime()) / (1000 * 60 * 60 * 24)
      );

      // Define intervalos diferentes baseado na idade dos dados
      const updateInterval = daysAgo <= 7 
        ? 30 * 1000  // 30 segundos para dados até 7 dias
        : BACKGROUND_REFRESH_INTERVAL; // 2 minutos para dados mais antigos

      updateIntervals[index] = setInterval(() => {
        fetchOpportunities(
          vendedor.ownerId,
          vendedor.dateStart,
          vendedor.dateStart,
          vendedor.currentPage,
          true
        )
          .then(result => {
            if (result) {
              setVendedores(prev => prev.map((v, i) => {
                if (i === index) {
                  return {
                    ...v,
                    opportunities: sortOpportunities([
                      ...v.opportunities.filter(op => 
                        !result.opportunities.some(newOp => newOp.crmId === op.crmId)
                      ),
                      ...result.opportunities
                    ]),
                    totalPages: result.pagination.totalPages
                  };
                }
                return v;
              }));
            }
          })
          .catch(console.error);
      }, updateInterval);
    });

    return () => {
      Object.values(updateIntervals).forEach(interval => clearInterval(interval));
    };
  }, [vendedores, fetchOpportunities]);

  // Função para carregar mais oportunidades
  const loadMoreOpportunities = useCallback(async (vendedorIndex: number) => {
    const vendedor = vendedores[vendedorIndex];
    
    if (vendedor.isLoadingMore) return;

    setVendedores(prev => prev.map((v, i) => 
      i === vendedorIndex ? { ...v, isLoadingMore: true } : v
    ));

    try {
      // Calcula a nova data inicial como o dia anterior à data atual
      const newDateStart = format(
        subDays(parseISO(vendedor.dateStart), 1),
        'yyyy-MM-dd'
      );

      const result = await fetchOpportunities(
        vendedor.ownerId,
        newDateStart,
        newDateStart,
        '1'
      );

      if (result) {
        // Filtra oportunidades duplicadas e mantém a ordenação por data mais recente
        const newOpportunities = sortOpportunities([
          ...vendedor.opportunities,
          ...result.opportunities.filter(newOpp => 
            !vendedor.opportunities.some(existingOpp => 
              existingOpp.crmId === newOpp.crmId
            )
          )
        ]);

        setVendedores(prev => prev.map((v, i) => 
          i === vendedorIndex ? {
            ...v,
            opportunities: newOpportunities,
            filteredOpportunities: v.filteredOpportunities 
              ? sortOpportunities([
                  ...v.filteredOpportunities,
                  ...result.opportunities.filter(newOpp => 
                    !v.filteredOpportunities!.some(existingOpp => 
                      existingOpp.crmId === newOpp.crmId
                    )
                  )
                ])
              : null,
            dateStart: newDateStart,
            isLoadingMore: false,
            currentPage: '1',
            totalPages: result.pagination.totalPages
          } : v
        ));
      }
    } catch (error) {
      console.error('Erro ao carregar mais oportunidades:', error);
      setVendedores(prev => prev.map((v, i) => 
        i === vendedorIndex ? { ...v, isLoadingMore: false } : v
      ));
    }
  }, [vendedores, fetchOpportunities]);

  // Modifica a função fetchVendedores para usar o intervalo de datas
  const fetchVendedores = async () => {
    try {
      // Adiciona timestamp para quebrar cache
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/vendedores?t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache'
        }
      });
      const data = await response.json();

      if (data.success) {
        const vendedoresPromises = data.data.map(async (vendedor: Vendedor) => {
          const result = await fetchOpportunities(
            vendedor.ownerId,
            dateRange.start,
            dateRange.end,
            '1'
          );
          
          const opportunities = sortOpportunities(result?.opportunities || []);
          return {
            ...vendedor,
            opportunities,
            filteredOpportunities: null,
            currentPage: result?.pagination.currentPage || '1',
            totalPages: result?.pagination.totalPages || '1',
            isLoadingMore: false,
            dateStart: dateRange.start
          };
        });

        const vendedoresComOportunidades = (await Promise.all(vendedoresPromises)).filter(Boolean);
        setVendedores(vendedoresComOportunidades as Vendedor[]);
      } else {
        throw new Error(data.error || 'Erro ao carregar vendedores');
      }
    } catch (error) {
      console.error('Erro ao carregar vendedores:', error);
      toast.error('Erro ao carregar vendedores. Tente novamente mais tarde.');
    } finally {
      setIsLoading(false);
    }
  };

  // Atualiza o useEffect para usar o dateRange
  useEffect(() => {
    let isMounted = true;

    const loadData = async () => {
      if (!isMounted) return;
      setIsLoading(true);
      await fetchVendedores();
    };

    loadData();

    return () => {
      isMounted = false;
    };
  }, [dateRange]);

  // Função que efetivamente faz a delegação
  const delegateLead = async (
    opportunity: Opportunity,
    fromVendedor: { ownerId: number; nome: string },
    toVendedor: { ownerId: number; nome: string }
  ) => {
    // Fecha o modal de confirmação se estiver aberto
    setDelegateConfirm({
      isOpen: false,
      opportunity: null,
      fromVendedor: null,
      toVendedor: null
    });

    // Salva o estado atual para possível reversão
    const previousState = [...vendedores];

    // Atualiza o estado imediatamente (Optimistic Update)
    setVendedores(prev => prev.map(v => {
      if (v.ownerId === fromVendedor.ownerId) {
        return {
          ...v,
          opportunities: v.opportunities.filter(op => op.crmId !== opportunity.crmId),
          filteredOpportunities: v.filteredOpportunities 
            ? v.filteredOpportunities.filter(op => op.crmId !== opportunity.crmId)
            : null
        };
      }
      if (v.ownerId === toVendedor.ownerId) {
        const updatedOpportunity = {
          ...opportunity,
          ownerName: toVendedor.nome
        };
        return {
          ...v,
          opportunities: sortOpportunities([...v.opportunities, updatedOpportunity]),
          filteredOpportunities: v.filteredOpportunities 
            ? sortOpportunities([...v.filteredOpportunities, updatedOpportunity])
            : null
        };
      }
      return v;
    }));

    try {
      const response = await fetch(
        `https://api.leadhub.registrese.app.br/leads/delegar-lead/${toVendedor.ownerId}?crmId=${opportunity.crmId}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
        }
      );

      if (!response.ok) {
        throw new Error('Falha ao delegar lead');
      }

      toast.success(`Lead delegado com sucesso para ${toVendedor.nome}`);
    } catch (error) {
      console.error('Erro ao delegar lead:', error);
      // Reverte o estado em caso de erro
      setVendedores(previousState);
      toast.error('Erro ao delegar lead. Tente novamente.');
    }
  };

  // Função para delegar lead
  const handleDelegateLead = async (
    opportunity: Opportunity,
    fromVendedor: { ownerId: number; nome: string },
    toVendedor: { ownerId: number; nome: string }
  ) => {
    // Se o vendedor já se apresentou, mostra confirmação
    if (opportunity.formData?.entradaLead) {
      setDelegateConfirm({
        isOpen: true,
        opportunity,
        fromVendedor,
        toVendedor
      });
      return;
    }

    // Se não se apresentou, delega direto
    await delegateLead(opportunity, fromVendedor, toVendedor);
  };

  // Atualiza o onDragEnd para usar a nova lógica
  const onDragEnd = (result: DropResult) => {
    const { source, destination } = result;

    if (!destination) return;

    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    ) {
      return;
    }

    const sourceVendedor = vendedores.find(v => v.ownerId.toString() === source.droppableId);
    const destVendedor = vendedores.find(v => v.ownerId.toString() === destination.droppableId);

    if (!sourceVendedor || !destVendedor) return;

    const movedOpportunity = { ...sourceVendedor.opportunities[source.index] };

    handleDelegateLead(
      movedOpportunity,
      { ownerId: sourceVendedor.ownerId, nome: sourceVendedor.nome },
      { ownerId: destVendedor.ownerId, nome: destVendedor.nome }
    );
  };

  // Função para atualizar todos os dados
  const handleRefreshAll = async () => {
    if (isRefreshing) return;
    
    setIsRefreshing(true);
    try {
      // Primeiro, busca os dados atualizados dos vendedores
      const timestamp = new Date().getTime();
      const vendedoresResponse = await fetch(`/api/vendedores?t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache'
        }
      });
      console.log('Vendedores Response:', vendedoresResponse);
      const vendedoresData = await vendedoresResponse.json();

      if (!vendedoresData.success) {
        throw new Error('Erro ao atualizar dados dos vendedores');
      }

      // Depois busca as oportunidades atualizadas para cada vendedor
      const refreshPromises = vendedoresData.data.map(async (vendedor: Vendedor) => {
        const result = await fetchOpportunities(
          vendedor.ownerId,
          vendedor.dateStart || format(subDays(new Date(), INITIAL_DAYS), 'yyyy-MM-dd'),
          vendedor.dateStart || format(subDays(new Date(), INITIAL_DAYS), 'yyyy-MM-dd'),
          '1',
          true
        );
        return { vendedor, result };
      });

      const results = await Promise.all(refreshPromises);
      
      // Atualiza o estado com os dados mais recentes
      setVendedores(results
        .filter(r => r.result !== null)
        .map(({ vendedor, result }) => ({
          ...vendedor,
          opportunities: sortOpportunities(result!.opportunities),
          filteredOpportunities: null,
          currentPage: '1',
          totalPages: result!.pagination.totalPages,
          isLoadingMore: false,
          dateStart: vendedor.dateStart || format(subDays(new Date(), INITIAL_DAYS), 'yyyy-MM-dd')
        }))
      );

      toast.success('Dados atualizados com sucesso!');
    } catch (error) {
      console.error('Erro ao atualizar dados:', error);
      toast.error('Erro ao atualizar dados');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Função para atualizar as oportunidades filtradas de um vendedor
  const handleFilter = (vendedorId: number, filteredOpportunities: Opportunity[]) => {
    setVendedores(prev => prev.map(v => {
      if (v.ownerId === vendedorId) {
        return {
          ...v,
          filteredOpportunities
        };
      }
      return v;
    }));
  };

  // Função para deletar uma oportunidade
  const handleDeleteOpportunity = async (
    opportunity: Opportunity,
    vendedorIndex: number
  ) => {
    // Salva o estado atual para possível reversão
    const previousState = [...vendedores];

    // Atualiza a UI imediatamente (Optimistic Update)
    setVendedores(prev => prev.map((v, index) => {
      if (index === vendedorIndex) {
        return {
          ...v,
          opportunities: v.opportunities.filter(op => op.crmId !== opportunity.crmId),
          filteredOpportunities: v.filteredOpportunities
            ? v.filteredOpportunities.filter(op => op.crmId !== opportunity.crmId)
            : null
        };
      }
      return v;
    }));

    try {
      const response = await fetch(
        `https://api.form-dash.registrese.app.br/opportunities/delete/${opportunity.crmId}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Falha ao excluir oportunidade');
      }

      const data = await response.json();
      if (data.success) {
        toast.success('Oportunidade excluída com sucesso!');
      } else {
        throw new Error(data.error || 'Falha ao excluir oportunidade');
      }
    } catch (error) {
      console.error('Erro ao excluir oportunidade:', error);
      // Reverte o estado em caso de erro
      setVendedores(previousState);
      toast.error('Erro ao excluir oportunidade. Tente novamente.');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-200px)]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen overflow-hidden">
      <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800">
        <div className="flex items-center gap-4">
          <h1 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
            Leads por Vendedor
          </h1>
          <DateFilter
            selectedDate={selectedDate}
            onDateChange={(range) => {
              setSelectedDate(range.start);
              setDateRange(range);
            }}
          />
          <LeadsDistribuicaoNotification
            vendedores={vendedores}
            onDelegate={async (leadId, toVendedorId) => {
              const toVendedor = vendedores.find(v => v.ownerId === toVendedorId);
              if (toVendedor) {
                try {
                  const response = await fetch(
                    `https://api.leadhub.registrese.app.br/leads/delegar-lead/${toVendedorId}?crmId=${leadId}`,
                    {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                      }
                    }
                  );

                  if (!response.ok) {
                    throw new Error('Falha ao delegar lead');
                  }

                  toast.success(`Lead delegado com sucesso para ${toVendedor.nome}`);
                  handleRefreshAll();
                } catch (error) {
                  console.error('Erro ao delegar lead:', error);
                  toast.error('Erro ao delegar lead. Tente novamente.');
                }
              }
            }}
            onUpdate={handleRefreshAll}
          />
        </div>
        <button
          onClick={handleRefreshAll}
          className={`ml-4 p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 rounded-full transition-all ${
            isRefreshing ? 'animate-spin' : ''
          }`}
          disabled={isRefreshing}
        >
          <FaSync />
        </button>
      </div>

      {/* Loading state */}
      {isLoading ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent"></div>
        </div>
      ) : (
        <DragDropContext onDragEnd={onDragEnd}>
          <div className={`flex justify-between gap-4 p-4 overflow-x-auto h-[calc(100vh-73px)] ${
            isFormDataModalOpen ? 'mr-[600px]' : ''
          }`}>
            {vendedores
              .sort((a, b) => a.nome.localeCompare(b.nome, 'pt-BR'))
              .map((vendedor, vendedorIndex) => (
              <Droppable key={vendedor.ownerId} droppableId={vendedor.ownerId.toString()}>
                {(provided) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    className="flex-shrink-0 w-[350px] bg-gray-100 dark:bg-gray-800 rounded-lg h-full flex flex-col overflow-hidden"
                  >
                    {/* Cabeçalho do vendedor */}
                    <div className="flex-shrink-0 p-4 bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="relative w-10 h-10 rounded-full overflow-hidden">
                            <Image
                              src={vendedor.avatar || '/default-avatar.png'}
                              alt={vendedor.nome}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-800 dark:text-gray-200">
                              {vendedor.nome}
                            </h3>
                            <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                              <span>
                                {vendedor.filteredOpportunities?.length || vendedor.opportunities.length} leads
                              </span>
                              <span className="text-gray-400 dark:text-gray-500">•</span>
                              <span>
                                {format(parseISO(vendedor.dateStart), "dd/MM/yyyy", { locale: ptBR })}
                              </span>
                            </div>
                          </div>
                        </div>
                        <VendedorFilter
                          opportunities={vendedor.opportunities}
                          onFilter={(filtered) => handleFilter(vendedor.ownerId, filtered)}
                          ownerId={vendedor.ownerId}
                          onDateChange={async (date) => {
                            try {
                              const result = await fetchOpportunities(
                                vendedor.ownerId,
                                date,
                                date,
                                '1',
                                true
                              );
                              
                              if (result) {
                                setVendedores(prev => prev.map(v => {
                                  if (v.ownerId === vendedor.ownerId) {
                                    return {
                                      ...v,
                                      opportunities: sortOpportunities(result.opportunities),
                                      filteredOpportunities: null,
                                      currentPage: result.pagination.currentPage,
                                      totalPages: result.pagination.totalPages,
                                      dateStart: date
                                    };
                                  }
                                  return v;
                                }));
                              }
                            } catch (error) {
                              console.error('Erro ao buscar oportunidades por data:', error);
                              toast.error('Erro ao buscar oportunidades para a data selecionada');
                            }
                          }}
                        />
                      </div>
                    </div>

                    {/* Lista de oportunidades */}
                    <div className="flex-1 overflow-y-auto p-4 space-y-4">
                      {(vendedor.filteredOpportunities || vendedor.opportunities).map((opp, index) => (
                        <Draggable key={opp.crmId} draggableId={opp.crmId.toString()} index={index}>
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              style={{
                                ...provided.draggableProps.style,
                                opacity: snapshot.isDragging ? 0.5 : 1,
                              }}
                            >
                              <KanbanCard
                                opportunity={opp}
                                currentVendedor={{
                                  ownerId: vendedor.ownerId,
                                  nome: vendedor.nome,
                                  avatar: vendedor.avatar
                                }}
                                vendedores={vendedores.map(v => ({
                                  ownerId: v.ownerId,
                                  nome: v.nome,
                                  avatar: v.avatar
                                }))}
                                onDelegate={(toVendedorId) => {
                                  const toVendedor = vendedores.find(v => v.ownerId === toVendedorId);
                                  if (toVendedor) {
                                    handleDelegateLead(
                                      opp,
                                      { ownerId: vendedor.ownerId, nome: vendedor.nome },
                                      { ownerId: toVendedor.ownerId, nome: toVendedor.nome }
                                    );
                                  }
                                }}
                                onDelete={() => handleDeleteOpportunity(opp, index)}
                                onUpdate={handleRefreshAll}
                              />
                            </div>
                          )}
                        </Draggable>
                      ))}

                      {provided.placeholder}

                      {/* Estado vazio ou botão de carregar mais */}
                      {(vendedor.filteredOpportunities || vendedor.opportunities).length === 0 ? (
                        <div className="text-center py-6 px-3 bg-gray-100 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600/50">
                          <p className="text-gray-500 dark:text-gray-400 text-sm">
                            {vendedor.filteredOpportunities ? 'Nenhum resultado encontrado' : 'Nenhum lead atribuído'}
                          </p>
                        </div>
                      ) : !vendedor.isLoadingMore && (
                        <button
                          onClick={() => loadMoreOpportunities(vendedorIndex)}
                          className="w-full mt-4 py-2 px-4 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
                        >
                          <span>Carregar mais leads</span>
                          <svg 
                            className="w-4 h-4" 
                            fill="none" 
                            stroke="currentColor" 
                            viewBox="0 0 24 24"
                          >
                            <path 
                              strokeLinecap="round" 
                              strokeLinejoin="round" 
                              strokeWidth={2} 
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </button>
                      )}

                      {/* Loading de mais oportunidades */}
                      {vendedor.isLoadingMore && (
                        <div className="flex justify-center py-3">
                          <div className="flex flex-col items-center space-y-2">
                            <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-500/80 border-t-transparent"></div>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              Carregando mais leads...
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </Droppable>
            ))}
          </div>
        </DragDropContext>
      )}

      {/* Modal fixo à direita */}
      {isFormDataModalOpen && (
        <div className="fixed right-0 top-0 bottom-0 w-[600px] bg-white dark:bg-gray-900 shadow-lg overflow-auto">
          <VendedorFormDataModal 
            vendedores={vendedores}
            currentVendedor={vendedores.find(v => v.opportunities.some(op => op.crmId === (typeof selectedFormData === 'string' ? JSON.parse(selectedFormData) : selectedFormData)?.crmId))}
            onDelegate={async (toVendedorId) => {
              const opportunity = typeof selectedFormData === 'string' ? JSON.parse(selectedFormData) : selectedFormData;
              const fromVendedor = vendedores.find(v => v.opportunities.some(op => op.crmId === opportunity?.crmId));
              const toVendedor = vendedores.find(v => v.ownerId === toVendedorId);
              
              if (fromVendedor && toVendedor && opportunity) {
                const fullOpportunity = fromVendedor.opportunities.find(op => op.crmId === opportunity.crmId);
                
                if (fullOpportunity) {
                  await handleDelegateLead(
                    fullOpportunity,
                    { ownerId: fromVendedor.ownerId, nome: fromVendedor.nome },
                    { ownerId: toVendedor.ownerId, nome: toVendedor.nome }
                  );
                }
              }
            }}
            onUpdate={handleRefreshAll}
          />
        </div>
      )}

      {/* Modal de confirmação de delegação */}
      {delegateConfirm.isOpen && delegateConfirm.opportunity && delegateConfirm.fromVendedor && delegateConfirm.toVendedor && (
        <DelegateConfirmModal
          isOpen={delegateConfirm.isOpen}
          onClose={() => setDelegateConfirm({
            isOpen: false,
            opportunity: null,
            fromVendedor: null,
            toVendedor: null
          })}
          onConfirm={() => delegateLead(
            delegateConfirm.opportunity!,
            delegateConfirm.fromVendedor!,
            delegateConfirm.toVendedor!
          )}
          fromVendedor={delegateConfirm.fromVendedor.nome}
          toVendedor={delegateConfirm.toVendedor.nome}
          entradaLead={format(
            parseISO(delegateConfirm.opportunity.formData?.entradaLead || ''),
            "dd/MM/yyyy 'às' HH:mm",
            { locale: ptBR }
          )}
        />
      )}
    </div>
  );
};

export default VendedoresKanban; 