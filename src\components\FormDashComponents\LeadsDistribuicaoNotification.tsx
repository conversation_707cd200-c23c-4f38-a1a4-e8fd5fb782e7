'use client';

import React, { useState, useEffect } from 'react';
import { FaBell } from 'react-icons/fa';
import { getLeadsDistribuicao } from '@/services/crmService';
import KanbanCard from './KanbanCard';
import { Portal } from '@/components/Portal';

interface LeadsDistribuicaoNotificationProps {
  vendedores: {
    ownerId: number;
    nome: string;
    avatar: string;
  }[];
  onDelegate: (leadId: string, toVendedorId: number) => void;
  onUpdate: () => void;
}

const LeadsDistribuicaoNotification: React.FC<LeadsDistribuicaoNotificationProps> = ({
  vendedores,
  onDelegate,
  onUpdate
}) => {
  const [leads, setLeads] = useState<any[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  const fetchLeads = async () => {
    try {
      setLoading(true);
      const leadsData = await getLeadsDistribuicao();
      setLeads(leadsData);
    } catch (error) {
      console.error('Erro ao buscar leads:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLeads();
    const interval = setInterval(fetchLeads, 60000); // Atualiza a cada minuto
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`relative p-2 transition-colors ${
          leads.length > 0
            ? 'text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300'
            : 'text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400'
        }`}
        title={leads.length > 0 ? `${leads.length} leads para distribuir` : 'Nenhum lead para distribuir'}
      >
        <FaBell size={20} />
        {leads.length > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {leads.length}
          </span>
        )}
      </button>

      {isOpen && (
        <Portal>
          <div className="fixed inset-0 bg-black/50 z-50" onClick={() => setIsOpen(false)} />
          <div className="fixed right-4 top-20 w-[400px] bg-white dark:bg-gray-800 rounded-lg shadow-xl z-50 max-h-[80vh] overflow-hidden flex flex-col">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                Leads para Distribuir {leads.length > 0 ? `(${leads.length})` : ''}
              </h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                ✕
              </button>
            </div>

            <div className="flex-1 overflow-y-auto p-4">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-500 border-t-transparent"></div>
                </div>
              ) : leads.length > 0 ? (
                <div className="space-y-4">
                  {leads.map((lead) => (
                    <KanbanCard
                      key={lead.id}
                      opportunity={{
                        id: lead.id,
                        crmId: lead.id,
                        brandTitle: lead.title,
                        stageName: "📥 Distribuir para Vendas",
                        isCompleted: false,
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString(),
                        formData: {
                          entradaLead: lead.created_at,
                          score: lead.score || 0,
                          leadNumber: lead.phone,
                          businessInternship: "Por enquanto é só um plano"
                        }
                      }}
                      currentVendedor={{
                        ownerId: 0,
                        nome: "Sem Vendedor",
                        avatar: "/default-avatar.png"
                      }}
                      vendedores={vendedores}
                      onDelegate={(toVendedorId) => onDelegate(lead.id, toVendedorId)}
                      onDelete={() => {}}
                      onUpdate={onUpdate}
                    />
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8 text-gray-500 dark:text-gray-400">
                  <FaBell size={40} className="mb-4 opacity-50" />
                  <p className="text-center">
                    Nenhum lead aguardando distribuição no momento.
                  </p>
                  <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">
                    Novos leads aparecerão aqui automaticamente.
                  </p>
                </div>
              )}
            </div>
          </div>
        </Portal>
      )}
    </div>
  );
};

export default LeadsDistribuicaoNotification; 