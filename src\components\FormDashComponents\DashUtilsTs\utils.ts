import { format, startOfWeek, addDays } from "date-fns";
import { ptBR } from "date-fns/locale";

export const getDaysOfWeek = (currentMonth: Date): string[] => {
  const days = [];
  const dateFormat = "EEE"; // Exibe o dia da semana abreviado
  const startDate = startOfWeek(currentMonth, { weekStartsOn: 0 }); // Começa no domingo

  for (let i = 0; i < 7; i++) {
    days.push(format(addDays(startDate, i), dateFormat, { locale: ptBR }));
  }

  return days;
};
