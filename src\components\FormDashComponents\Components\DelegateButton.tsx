import React, { useState } from "react";
import { toast } from "react-toastify";
import axios from "axios";
import useOpportunityStore, {
  OpportunityStore,
} from "@/context/useOpportunityStore";
import { Opportunity } from "@/types/crm";

interface DelegateButtonProps {
  crmId: string;
  brandTitle: string;
  opportunity: Opportunity;
  setDelegated: (isDelegated: boolean) => void;
}

export const DelegateButton: React.FC<DelegateButtonProps> = ({
  crmId,
  brandTitle,
  opportunity,
  setDelegated,
}) => {
  const [isDelegating, setIsDelegating] = useState<boolean>(false);

  // Obtém apenas updateOpportunity do store
  const updateOpportunity = useOpportunityStore(
    (state: OpportunityStore) => state.updateOpportunity
  );

  const handleDelegate = async () => {
    setIsDelegating(true);

    try {
      // Chamada para a API de delegação
      const response = await axios.post(
        "https://api.registrese.app.br/webhook/chatguru/delegarcrm",
        {
          campos_personalizados: {
            ID_CRM: crmId,
            Nome_da_marca: brandTitle,
          },
        }
      );

      if (response.status !== 200) {
        throw new Error("Falha na delegação");
      }

      // Exibe uma mensagem de sucesso
      toast.success(`Lead ${crmId} delegado com sucesso!`);

      // Verifica se todas as propriedades obrigatórias estão presentes
      if (
        opportunity &&
        opportunity.id !== undefined &&
        opportunity.pipelineId !== undefined &&
        opportunity.pipelineName !== undefined &&
        opportunity.stageName !== undefined &&
        opportunity.createdAt !== undefined &&
        opportunity.closedAt !== undefined &&
        opportunity.ownerName !== undefined
      ) {
        // Atualiza o stageId, stageName e lastStageUpdatedAt
        const updatedOpportunity: Opportunity = {
          ...opportunity,
          stageId: "488514", // Atualiza o stageId para "Distribuir pra Vendas"
          stageName: "Distribuir pra Vendas",
        };

        // Atualiza a oportunidade usando a função da store
        updateOpportunity(updatedOpportunity);

        setDelegated(true);
      } else {
        console.error(
          `Oportunidade ${crmId} possui propriedades obrigatórias faltando.`
        );
        toast.error(
          `Erro ao delegar lead ${crmId}. Dados inválidos na oportunidade.`
        );
      }
    } catch (error) {
      console.error("Erro ao delegar lead:", error);
      toast.error(`Erro ao delegar lead ${crmId}. Por favor, tente novamente.`);
    } finally {
      setIsDelegating(false);
    }
  };

  return (
    <button
      className={`px-4 py-2 transition-all duration-200 shadow-sm rounded ${
        isDelegating
          ? "border border-gray-300 bg-gray-100 text-gray-500 cursor-not-allowed dark:bg-gray-700 dark:border-gray-500 dark:text-gray-400"
          : "border border-blue-500 bg-white text-blue-500 hover:bg-blue-500 hover:text-white dark:bg-gray-800 dark:border-blue-600 dark:text-blue-400 dark:hover:bg-blue-700 dark:hover:text-white"
      } text-sm font-semibold`}
      onClick={handleDelegate}
      disabled={isDelegating}
    >
      {isDelegating ? "Delegando..." : "Delegar"}
    </button>
  );
};
