// src/components/CalendarDays.tsx
import React from "react";
import { getDaysOfWeek } from "../DashUtilsTs/utils";

interface CalendarDaysProps {
  currentMonth: Date;
}

const CalendarDays: React.FC<CalendarDaysProps> = ({ currentMonth }) => {
  const daysOfWeek = getDaysOfWeek(currentMonth);

  return (
    <div className="grid grid-cols-7 gap-2 mb-2">
      {daysOfWeek.map((day, index) => (
        <div
          key={index}
          className="p-2 text-center font-semibold text-gray-700 dark:text-gray-200 dark:bg-gray-700 bg-gray-200 rounded"
        >
          {day}
        </div>
      ))}
    </div>
  );
};

export default CalendarDays;
