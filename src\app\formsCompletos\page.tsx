"use client";

import React, { useEffect } from "react";
import useAuth from "@/context/useAuthStore";


const FormsCompletosPage = () => {

  const token = useAuth((state) => state.token);
  const setShowLoginModal = useAuth((state) => state.setShowLoginModal);

  useEffect(() => {
    if (token) {
        setShowLoginModal(true);
    }
  }, [token,setShowLoginModal]);
  return(
      <div className="container flex justify-center items-center h-screen text-verde">
        <h1>Em construção</h1>
      </div>
  )
  //<CompletedFormTabContent />;
};

export default FormsCompletosPage;
