import React, { useCallback, useState } from 'react';
import { MarketingFilters as FiltersType } from '@/types/marketing';
import { FunnelIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';
import debounce from 'lodash/debounce';

interface MarketingFiltersProps {
  filters: FiltersType;
  onFilterChange: (filters: Partial<FiltersType>) => void;
  onExport: () => void;
}

const MarketingFilters: React.FC<MarketingFiltersProps> = ({
  filters,
  onFilterChange,
  onExport,
}) => {
  const [searchInputValue, setSearchInputValue] = useState(filters.searchTerm || '');

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      onFilterChange({ searchTerm: value });
    }, 300),
    [onFilterChange]
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchInputValue(value);
    debouncedSearch(value);
  };

  const handleDateChange = (field: 'startDate' | 'endDate', value: string) => {
    // Se tiver uma data, adiciona o horário UTC (início ou fim do dia)
    if (value) {
      const date = new Date(value);
      if (field === 'startDate') {
        date.setUTCHours(0, 0, 0, 0);
      } else {
        date.setUTCHours(23, 59, 59, 999);
      }
      onFilterChange({ [field]: date.toISOString() });
    } else {
      onFilterChange({ [field]: '' });
    }
  };

  // Converte a data UTC para local para exibição no input
  const getLocalDate = (utcDate: string) => {
    if (!utcDate) return '';
    const date = new Date(utcDate);
    return new Date(date.getTime() - date.getTimezoneOffset() * 60000)
      .toISOString()
      .split('T')[0];
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Data Início
            </label>
            <input
              type="date"
              value={getLocalDate(filters.startDate || '')}
              onChange={(e) => handleDateChange('startDate', e.target.value)}
              className="w-full rounded-md border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm h-8 text-gray-900 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Data Fim
            </label>
            <input
              type="date"
              value={getLocalDate(filters.endDate || '')}
              onChange={(e) => handleDateChange('endDate', e.target.value)}
              className="w-full rounded-md border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm h-8 text-gray-900 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Buscar em Campanhas
            </label>
            <input
              type="text"
              value={searchInputValue}
              onChange={handleSearchChange}
              placeholder="Buscar em ID, UTM Source, Medium ou Campaign"
              className="w-full rounded-md border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm h-8 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
            />
          </div>
        </div>

        <div className="mt-4 flex justify-end">
          <button
            onClick={onExport}
            className="inline-flex items-center justify-center gap-1 px-3 h-8 border border-transparent rounded-md text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
          >
            <ArrowDownTrayIcon className="h-4 w-4" />
            <span>CSV</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default MarketingFilters; 