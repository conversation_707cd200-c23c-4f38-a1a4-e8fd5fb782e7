import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const API_KEY = process.env.API_KEY;
  const searchParams = request.nextUrl.searchParams;
  const authHeader = request.headers.get('Authorization');

  if (!authHeader) {
    return NextResponse.json({ error: 'Token não fornecido' }, { status: 401 });
  }

  try {
    const response = await fetch(
      `https://registre.se/api/email-campaign/leads?${searchParams.toString()}`,
      {
        headers: {
          'x-api-key': API_KEY || '',
          'Authorization': authHeader
        },
      }
    );

    if (!response.ok) {
      throw new Error('Falha ao carregar leads');
    }

    const data = await response.json();
    return NextResponse.json(data);
    
  } catch (error) {
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Erro desconhecido' },
      { status: 500 }
    );
  }
} 