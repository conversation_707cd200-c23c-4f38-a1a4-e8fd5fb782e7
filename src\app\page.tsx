// frontend/pages/DashboardPage.tsx

"use client";
import React, { useEffect } from "react";
import useAuth from "@/context/useAuthStore";
import Spinner from "@/components/common/Spinner";
import DashboardCharts from "@/components/FormDashComponents/Charts/DashboardCharts";
import useOpportunityStore from "@/context/useOpportunityStore";

const DashboardPage = () => {
  const token = useAuth((state) => state.token);
  const setShowLoginModal = useAuth((state) => state.setShowLoginModal);

  const {
    initialChartData,
    stackedBarChartData,
    isLoadingInitialChartData,
    isLoadingStackedBarChartData,
    errorInitialChartData,
    errorStackedBar<PERSON>hartData,
    fetchInitialChartData,
    fetchStackedBarChartData,
  } = useOpportunityStore();

  useEffect(() => {
    if (!token) {
      setShowLoginModal(true);
      return;
    }

    // Buscar dados dos gráficos
    fetchInitialChartData(token);
    fetchStackedBarChartData(token);
  }, [
    token,
    setShowLoginModal,
    fetchInitialChartData,
    fetchStackedBarChartData,
  ]);

  // Verifica se qualquer uma das operações de carregamento está em progresso
  if (isLoadingInitialChartData || isLoadingStackedBarChartData) {
    return <Spinner />;
  }

  // Verifica se há algum erro nas operações de fetch
  if (errorInitialChartData || errorStackedBarChartData) {
    return <div>Erro: {errorInitialChartData || errorStackedBarChartData}</div>;
  }

  // Certifique-se de que os dados dos gráficos foram carregados antes de renderizar
  if (!initialChartData || !stackedBarChartData) {
    return <div>Carregando dados dos gráficos...</div>;
  }

  return (
    <DashboardCharts
      initialChartData={initialChartData}
      stackedBarChartData={stackedBarChartData}
    />
  );
};

export default DashboardPage;
