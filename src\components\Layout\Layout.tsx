/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */

// components/Layout/Layout.tsx
"use client";

import React, { ReactNode, useEffect } from "react";
import Sidebar from "@/components/FormDashComponents/SideBar";
import MetaTags from "@/components/FormDashComponents/MetaTagsNoTrack";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import LoginDashModal from "@/components/FormDashComponents/Components/LoginDashModal";
import useAuth from "@/context/useAuthStore";
import { useRouter } from "next/navigation";

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { login, logout, token, showLoginModal, setShowLoginModal } = useAuth();

  const router = useRouter();

  useEffect(() => {
    if (!token) {
      setShowLoginModal(true);
    }
  }, [token, setShowLoginModal]);

  const handleLoginSuccess = (token: string) => {
    login(token);
    setShowLoginModal(false);
    router.push("/");
  };

  const handleLogout = () => {
    logout();
    router.push("/");
  };

  return (
    <>
      <MetaTags
        title="Formulários - Registre.se"
        description="Painel de controle"
        keywords="registro, marca, inpi, wipo, assessoria"
        twitterCardType="summary"
      />
      <ToastContainer />
      {token ? (
        <div className="h-screen flex overflow-hidden">
          <Sidebar onLogout={handleLogout} />
          <div className="flex-1 p-2  bg-gray-50 dark:bg-gray-800 overflow-hidden">
            <div className="h-full mx-auto w-full">
              {children}
            </div>
          </div>
        </div>
      ) : (
        showLoginModal && <LoginDashModal onSuccess={handleLoginSuccess} />
      )}
    </>
  );
};

export default Layout;
