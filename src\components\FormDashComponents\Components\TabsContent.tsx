// components/FormDashComponents/TabsContent.tsx
/* eslint-disable */
import React, { useMemo } from "react";
import DashboardCharts from "@/components/FormDashComponents/Charts/DashboardCharts";
import CompletedFormTabContent from "./CompletedFormTabContent";
import InProgressFormTabContent from "./InProgresFormTabContent";
import LogsTabContent from "./LogsTabContent";
import useOpportunityStore from "@/context/useOpportunityStore";
import { Opportunity } from "@/types/crm";


interface TabsContentProps {
  activeTab: "initial" | "completed" | "inProgress" | "logs";
}
const TabsContent: React.FC<TabsContentProps> = ({
  activeTab
}) => {
  const { opportunities } = useOpportunityStore();

  const completedForms = useMemo(
    () => opportunities.filter((opp:Opportunity) => opp.isCompleted),
    [opportunities]
  );
  const inProgressForms = useMemo(
    () => opportunities.filter((opp:Opportunity) => !opp.isCompleted),
    [opportunities]
  );
  switch (activeTab) {
    case "initial":
      return (
        <DashboardCharts
          opportunities={opportunities}
        />
      );
    case "completed":
      return <CompletedFormTabContent />;
    case "inProgress":
      return <InProgressFormTabContent />;
    case "logs":
      return (
        <LogsTabContent
          opportunities={opportunities}
        />
      );
    default:
      return null;
  }
};

export default TabsContent;
