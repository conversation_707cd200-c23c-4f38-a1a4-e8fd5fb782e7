import { create } from "zustand";
import { FormData } from "@/types/crm";

interface FormDataModalState {
  isFormDataModalOpen: boolean;
  selectedFormData: FormData | null;
  openFormDataModal: () => void; // Para abrir o modal
  closeFormDataModal: () => void; // Para fechar o modal
  setSelectedFormData: (formData: FormData | null) => void; // Para definir os dados do modal
}

export const useFormDataModalStore = create<FormDataModalState>((set) => ({
  isFormDataModalOpen: false,
  selectedFormData: null,
  openFormDataModal: () => set({ isFormDataModalOpen: true }),
  closeFormDataModal: () => set({ isFormDataModalOpen: false }),
  setSelectedFormData: (formData) => set({ selectedFormData: formData }),
}));
