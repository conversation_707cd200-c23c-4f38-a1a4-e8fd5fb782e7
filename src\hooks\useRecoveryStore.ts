import { create } from 'zustand';

interface RecoveryStore {
  processingLeads: Record<string, boolean>;
  setProcessingLead: (crmId: string, isProcessing: boolean) => void;
  isProcessing: (crmId: string) => boolean;
}

export const useRecoveryStore = create<RecoveryStore>((set, get) => ({
  processingLeads: {},
  setProcessingLead: (crmId: string, isProcessing: boolean) =>
    set((state) => ({
      processingLeads: {
        ...state.processingLeads,
        [crmId]: isProcessing,
      },
    })),
  isProcessing: (crmId: string) => get().processingLeads[crmId] || false,
})); 