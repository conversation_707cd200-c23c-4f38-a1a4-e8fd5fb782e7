import { NextResponse } from 'next/server';
import dns from 'dns';
import { promisify } from 'util';

const resolveMx = promisify(dns.resolveMx);

// Lista de domínios temporários ou inválidos
const invalidDomains = [
  'temp-mail.org',
  'tempmail.com',
  'disposable.com',
  'mailinator.com',
  'fakeinbox.com',
  'example.com',
  'test.com',
  'kkd.com',
  'yopmail.com',
  'guerrillamail.com',
  '10minutemail.com',
  'mailnesia.com',
  'throwawaymail.com',
  'sharklasers.com',
  'spam4.me',
  'grr.la',
  'maildrop.cc',
  'getairmail.com',
  'mailnull.com',
  'spamgourmet.com',
  'trashmail.com',
  'mailinator.net',
  'mailinator.org',
  'dispostable.com',
  'tempr.email',
  'tempmail.ninja',
  'tempmail.space',
  'tempmailaddress.com',
];

// Regex mais robusta para validação de email
const emailRegex = /^(?:[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?|\[(?:(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9])|[a-zA-Z0-9-]*[a-zA-Z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])$/;

export async function POST(request: Request) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json({ isValid: false, reason: 'Email não fornecido' });
    }

    // Validação básica com regex
    if (!emailRegex.test(email)) {
      return NextResponse.json({ isValid: false, reason: 'Formato de email inválido' });
    }

    // Extrair o domínio
    const domain = email.split('@')[1].toLowerCase();

    // Verificar se é um domínio conhecido como inválido/temporário
    if (invalidDomains.includes(domain)) {
      return NextResponse.json({ isValid: false, reason: 'Domínio temporário ou inválido' });
    }

    try {
      // Verificar registros MX
      const mxRecords = await resolveMx(domain);
      
      if (!mxRecords || mxRecords.length === 0) {
        return NextResponse.json({ isValid: false, reason: 'Domínio não possui registros MX válidos' });
      }

      // Verificações adicionais de qualidade dos registros MX
      const validMxHosts = mxRecords.some(record => {
        const host = record.exchange.toLowerCase();
        return !invalidDomains.some(invalidDomain => host.includes(invalidDomain));
      });

      if (!validMxHosts) {
        return NextResponse.json({ isValid: false, reason: 'Registros MX suspeitos' });
      }

      return NextResponse.json({ isValid: true });
    } catch (error) {
      return NextResponse.json({ isValid: false, reason: 'Erro ao verificar registros MX' });
    }
  } catch (error) {
    return NextResponse.json({ isValid: false, reason: 'Erro interno do servidor' });
  }
} 