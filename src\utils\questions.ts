interface QuestionData {
  name: string;
  title: string;
  subtitle?: string;
  placeholder?: string;
  iconUrl?: string;
  isPhoneNumber?: boolean;
  isEmail?: boolean;
  options?: Record<string, IOption>;
  multipleChoice?: boolean;
}
interface SubSelections {
  title?: string;
  answer?: string;
  name?: string;
  subSelectionsOptions?: string[];
  multipleChoice?: boolean;
}

export interface IOption {
  map?(
    arg0: (option: unknown) => import("react").JSX.Element
  ): import("react").ReactNode;
  title?: string;
  answer?: string;
  name?: string;
  placeholder?: string;
  iconUrl?: string;
  subSelections?: SubSelections;
  multipleChoice?: boolean
}

export const questions: { [key: string]: QuestionData } = {
  question1: {
    title: "Consulta de Viabilidade",
    subtitle:
      "Solicite a análise de um especialista para descobrir se sua marca está disponível para registro e quais os investimentos necessários.",
    placeholder: "Qual o nome da marca que deseja consultar?",
    iconUrl: "/images/form/search-icon.svg",
    name: "brandTitle",
  },
  question2: {
    title: "Qual é o seu nome?",
    iconUrl: "/images/form/text-icon.svg",
    name: "leadName",
  },
  question3: {
    title: "Qual o seu WhatsApp?",
    subtitle: "É para esse número que iremos enviar o resultado da análise.",
    placeholder: "(00) 99999-9999",
    iconUrl: "/images/form/text-icon.svg",
    name: "leadNumber",
    isPhoneNumber: true,
  },
  question4: {
    title: "Qual o seu E-mail?",
    placeholder: "<EMAIL>",
    iconUrl: "/images/form/text-icon.svg",
    name: "leadMail",
    isEmail: true,
  },
  question5: {
    title: "Há quanto tempo essa marca está em utilização?",
    name: "businessInternship",
    options: {
      option1: {
        answer: "Por enquanto é só um plano",
        subSelections: {
          title: "Certo! E qual seu objetivo com essa consulta?",
          name: "consultationPurpose",
          subSelectionsOptions: [
            "Somente saber se está disponível.",
            "Se estiver disponível, quero registrar.",
          ],
        },
      },
      option2: {
        answer: "Pretendo começar em breve",
        subSelections: {
          title: "Legal! E qual seu objetivo com essa consulta?",
          name: "consultationPurpose",
          subSelectionsOptions: [
            "Confirmar se o nome está disponível, antes de criar o logotipo.",
            "Logotipo está pronto e quero registrar antes de iniciar a divulgação.",
            "Não tenho logotipo ainda, mas quero garantir a propriedade do nome.",
          ],
        },
      },
      option3: {
        answer: "Comecei recentemente",
      },
      option4: {
        answer: "Já atuo há mais de 6 meses",
      },
      option5: {
        answer: "Já atuo há mais de 2 anos",
      },
    },
  },
  question6: {
    title: "Quantos colaboradores compõem o seu negócio?",
    name: "businessCollaborators",
    options: {
      option1: {
        answer: "1 a 2",
      },
      option2: {
        answer: "3 a 5",
      },
      option3: {
        answer: "6 a 10",
      },
      option4: {
        answer: "11 a 50",
      },
      option5: {
        answer: "Mais de 50",
      },
    },
  },
  question7: {
    title: "Possui CNPJ?",
    name: "haveCNPJ",
    options: {
      option1: {
        answer: "Sim.",
        subSelections: {
          title: "Qual o enquadramento fiscal da empresa?",
          name: "taxFramework",
          subSelectionsOptions: [
            "MEI",
            "ME",
            "EPP",
            "DEMAIS",
            "Não sei responder.",
          ],
        },
      },
      option2: {
        answer: "Não, mas estamos providenciando.",
        subSelections: {
          title: "Qual será o enquadramento fiscal da empresa?",
          name: "notCnpjTaxFramework",
          subSelectionsOptions: [
            "MEI",
            "ME",
            "EPP",
            "DEMAIS",
            "Não sei responder",
          ],
        },
      },
      option3: {
        answer: "Atuo como pessoa física",
      },
    },
  },
  question8: {
    title: "Qual das alternativas abaixo melhor define o seu negócio?",
    name: "businessDefinition",
    options: {
      option1: {
        answer: "Vestuário",
        subSelections: {
          title: "Selecione as alternativas que são adequadas ao seu negócio:",
          name: "clothingActing",
          multipleChoice: true,
          subSelectionsOptions: [
            "Tenho peças de marca própria",
            "Pretendo um dia ter peças de marca própria",
            "Revendo peças de outras marcas",
            "Vendo Roupas",
            "Vendo Calçados",
            "Vendo Bolsas",
          ],
        },
      },
      option2: {
        answer: "Fabricação de produtos",
        placeholder: "Descreva de forma detalhada a sua atuação",
        iconUrl: "/images/form/text-icon.svg",
        subSelections: {
          name: "businessDefinitionDescription",
          title: "Quais são os produtos que você comercializa?",
        },
      },
      option3: {
        answer: "Comércio de produtos de outras marcas",
        placeholder: "Descreva de forma detalhada a sua atuação",
        iconUrl: "/images/form/text-icon.svg",
        subSelections: {
          name: "businessDefinitionDescription",
          title: "Quais são os produtos que você comercializa?",
        },
      },
      option4: {
        answer: "Prestação de serviço",
        placeholder: "Descreva de forma detalhada a sua atuação",
        iconUrl: "/images/form/text-icon.svg",
        subSelections: {
          name: "businessDefinitionDescription",
          title: "Quais são os serviços que você realiza?",
        },
      },
      option5: {
        answer: "Software e/ou tecnologia",
        placeholder: "Descreva de forma detalhada a sua atuação",
        iconUrl: "/images/form/text-icon.svg",
        subSelections: {
          name: "businessDefinitionDescription",
          title: "Legal! Conta um pouco melhor o que você faz?",
        },
      },
      option6: {
        answer: "Outros",
        placeholder: "Descreva seu ramo de atuação?",
        iconUrl: "/images/form/text-icon.svg",
        subSelections: {
          name: "businessDefinitionDescription",
          title: "Descreva seu ramo de atuação?",
        },
      },
    },
  },
  question9: {
    title: "Já possui site ou redes sociais?",
    name: "clientSiteOrSocialLink",
    options: {
      option1: {
        answer: "Sim",
        placeholder: "URL do site e/ou @ do Instagram",
        iconUrl: "/images/form/text-icon.svg",
        subSelections: {
          title: "Legal! Conta para gente como podemos te encontrar:",
          name: "clientLink",
        },
      },
      option2: {
        answer: "Ainda não",
      },
    },
  },
  question10: {
    title: "O que te motivou a buscar o registro da marca?",
    subtitle: "Escolha mais de uma alternativa, se desejar",
    name: "reasonRegistration",
    options: {
      option1: {
        answer: "Quero ter exclusividade",
      },
      option2: {
        answer: "Quero ganhar credibilidade",
      },
      option3: {
        answer: "Tenho receio de perder a marca",
      },
      option4: {
        answer: "Descobri uma marca igual ou similar",
      },
      option5: {
        answer: "Desejo franquear a marca",
      },
      option6: {
        answer: "Recebi uma notificação judicial",
      },
      option7: {
        answer: "Curiosidade em saber como funciona",
      },
    },
  },
  question11: {
    title: "Qual o seu grau de conhecimento com relação ao registro?",

    name: "degreeOfKnowledge",
    options: {
      option1: {
        answer: "Nenhum",
      },
      option2: {
        answer: "Já pesquisei em alguns sites sobre o assunto",
      },
      option3: {
        answer: "Já fiz contato com outra assessoria",
      },
      option4: {
        answer: "Já registrei uma marca antes",
      },
    },
  },
  question12: {
    title: "O quanto você já investiu na sua marca?",

    name: "investmentInTheBrand",
    options: {
      option1: {
        answer: "Por enquanto nada",
      },
      option2: {
        answer: "Mais de 1 mil",
      },
      option3: {
        answer: "Mais de 10 mil",
      },
      option4: {
        answer: "Mais de 25 mil",
      },
      option5: {
        answer: "Mais de 50 mil",
      },
    },
  },
  question13: {
    title: "Em quanto tempo você gostaria de iniciar o processo de registro?",
    name: "whenToStartProcess",
    options: {
      option1: {
        answer: "Ainda hoje, se possível",
      },
      option2: {
        answer: "Em 1 ou 2 dias",
      },
      option3: {
        answer: "Em 1 ou 2 semanas",
      },
      option4: {
        answer: "Em 1 ou 2 meses",
      },
      option5: {
        answer: "Não tenho um prazo",
      },
    },
  },
  question14: {
    title: "Para finalizar: Como foi que você nos conheceu?",
    name: "howClientFoundUs",
    options: {
      option1: {
        answer: "Pesquisando no Google",
      },
      option2: {
        answer: "Instagram",
      },
      option3: {
        answer: "Facebook",
      },
      option4: {
        answer: "Youtube",
      },
      option5: {
        answer: "Indicação de um amigo",
      },
      option6: {
        answer: "Indicação de influenciador",
      },
      option7: {
        placeholder: "Outro",
        iconUrl: "/images/form/text-icon.svg",
      },
    },
  },
};
export const anotherBrandQuestions: { [key: string]: QuestionData } = {
  question1: {
    title: "Qual o nome da outra marca que deseja consultar?",
    name: "anotherBrandName",
    placeholder: "Qual o nome da marca que deseja consultar?",
    iconUrl: "/images/form/search-icon.svg",
  },
  question2: {
    title: "O ramo de atuação é o mesmo da marca anterior?",
    name: "businessSegment",
    placeholder: "Digite o ramo de atuação",
    iconUrl: "/images/form/text-icon.svg",
    options: {
      option1: {
        answer: "Sim",
      },
      option2: {
        answer: "Não",
        placeholder: "Descreva qual é o ramo de atuação...",
        iconUrl: "/images/form/text-icon.svg",
        subSelections: {
          title: "Descreva qual é o ramo de atuação:",
          name: "anotherBrandSegment",
        },
      },
    },
  },
};
