// types/interfaces.ts

// Interface para os logs associados às oportunidades
export interface Log {
  id: number;
  timestamp: Date | string;
  log: string;
  opportunityId: number;
  Opportunity?: Opportunity;
}
// frontend/types/chartTypes.ts

export interface InitialChartData {
  labels: string[];
  incompleteCounts: number[];
  completeCounts: number[];
}

export interface StackedBarChartData {
  labels: string[];
  sales: number[];
  nonSales: number[];
  notDistributed: number[];
  recovery: number[];
  leads: {
    sales: Opportunity[];
    nonSales: Opportunity[];
    notDistributed: Opportunity[];
    recovery: Opportunity[];
  };
}

// Interface para o estágio individual dentro de `stageHistories`
export interface StageHistory {
  in_stage_id: string;
  out_stage_id?: string | null;
  created_at: string; // ISO string
  updated_at: string; // ISO string
  lead_time?: string; // Tempo passado no estágio
}

// Interface para o estágio inicial `startedInStage`
export interface StartedInStage {
  id: string;
  name: string;
  created_at: string; // ISO string
  hash?: string;
}

// Interface para os detalhes do CRM associados a uma oportunidade
export interface CRMDetails {
  id: number;
  opportunityId: number;
  pipelineId?: string;
  pipelineName?: string;
  stageId?: string;
  stageName?: string;
  createdAt?: string; // ISO string
  closedAt?: string; // ISO string
  lastStageUpdatedAt?: string; // ISO string
  status?: number;
  value?: number;
  lastContactAt?: string; // ISO string
  stageHistories?: StageHistory[]; // Histórico de estágios como array
  startedInStage?: StartedInStage; // Estágio inicial como objeto
  lastStageHistory?: StageHistory; // Último estágio
  updatedAt: string; // ISO string
}

// Interface para os dados do formulário associados a uma oportunidade
export interface FormData {
  id?: number;
  crmId?: string;
  crmHash?: string;
  formProgress?: string;
  isCompleted?: boolean;
  createdAt?: string; // ISO string
  updatedAt?: string; // ISO string
  dataCadastro?: string; // ISO string
  entradaLead?: string; // ISO string
  apresentacao?: string; // ISO string
  score?: number;
  formData?: string;
  conversionTime?: string; // ISO string
  firstVisitTime?: string; // ISO string
  formHash?: string;
  leadMail?: string;
  leadEmail?: string; // Novo campo para email principal
  leadName?: string;
  leadNumber?: string;
  allPhones?: string[]; // Novo campo para lista de telefones
  allEmails?: string[]; // Novo campo para lista de emails
  deleted?: boolean; // Status de deleção da oportunidade
  utmCampaign?: string;
  utmSource?: string;
  pipelineId?: string;
  pipelineName?: string;
  stageId?: string;
  stageName?: string;
  lastStageUpdatedAt?: string; // ISO string
  closedAt?: string; // ISO string
  value?: number;
  lastContactAt?: string; // ISO string
  brandTitle?: string;
  businessCollaborators?: string;
  businessDefinition?: string;
  businessDefinitionDescription?: string;
  businessInternship?: string;
  clientLink?: string;
  clientSiteOrSocialLink?: string;
  consultationPurpose?: string;
  degreeOfKnowledge?: string;
  haveCNPJ?: string;
  howClientFoundUs?: string;
  investmentInTheBrand?: string;
  notCnpjTaxFramework?: string;
  reasonRegistration?: string;
  taxFramework?: string;
  inRecovery?: boolean;
  whenToStartProcess?: string;
  Log?: Log[]; // Logs relacionados à oportunidade
  trackingLead?: TrackingLead; // Tracking relacionado à oportunidade
  origem?: string;
  cidade?: string;
  uf?: string;
  dataFechamento?: string;
  horaVisita?: string;
  horaConversao?: string;
  cpf?: string;
  cnpj?: string;
  status?: number;
  situacao?: number;
  valor?: number;
  leadTime?: number;
  temLogotipo?: boolean;
  canal?: string;
  tipoNegocio?: string;
  colaboradores?: string;
  urlConversao?: string;
  temCNPJ?: string;
  campanha?: string;
  tipo?: string;
  conteudo?: string;
  keyword?: string;
  origemCampanha?: string;
  classes?: string;
  indicadoPor?: string;
  porte?: string;
  comoFoiAtendido?: string;
  motivoPerda?: number;
  formaPagamento?: string;
  primeiroVencimento?: string;
  valorCobrar?: number;
  condicaoPagamento?: string;
  parcelas?: number;
  valorParcela?: number;
  valorDemaisParcelas?: number;
  dataSegundoVencimento?: string;
  protocolo?: string;
  viabilidade?: string;
  rangeScore?: string;
  negociacao?: string;
  tipoLead?: string;
  tokenSite?: string;
  pTime?: string;
  proposta?: string;
  idOriginal?: string;
  faseDoPlanejamento?: string;
  tempoResolver?: string;
  investiuMarca?: string;
  ramoAtuacao?: string;
  redesSociaisSite?: string;
  tipoNegocioLead?: string;
  analiseCredito?: string;
  plano?: string;
  motivoRegistro?: string;
  dispositivo?: string;
  utmFinal?: string;
  historicoCampanhas?: string;
}

// Interface para as oportunidades
export interface Opportunity {
  id: number;
  crmId: string;
  completed?: boolean;
  crmHash?: string;
  deleted?: boolean;
  inRecovery?: boolean;
  status?: number;
  formHash?: string;
  score?: number;
  pipelineId?: string;
  pipelineName?: string;
  stageId?: string;
  stageName?: string;
  stageHistories?: StageHistory[]; // Histórico de estágios como array
  lastStageHistory?: StageHistory; // Último estágio
  startedInStage?: StartedInStage; // Estágio inicial
  formProgress?: string;
  utmSource?: string;
  utmCampaign?: string;
  firstVisitTime?: string; // ISO string
  conversionTime?: string; // ISO string
  leadName?: string;
  leadMail?: string;
  leadEmail?: string; // Novo campo para email principal
  leadNumber?: string;
  brandTitle: string;
  isCompleted: boolean;
  ownerName?: string;
  formData?: FormData; // Agora usando a interface FormData
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
  Log?: Log[]; // Logs relacionados à oportunidade
  trackingLead?: TrackingLead; // Tracking relacionado à oportunidade
  value?: number;
  closedAt?: string; // ISO string
  lastContactAt?: string; // ISO string
}

// Interface para o rastreamento de leads (TrackingLead)
export interface TrackingLead {
  id: number;
  createdAt?: string; // ISO string
  crmId: number;
  submitted: boolean;
  opportunityId: number;
  tracking: boolean;
  updatedAt?: string; // ISO string
  stageId?: number;
  Opportunity?: Opportunity;
}

// Interface para as configurações
export interface Settings {
  id: number;
  lastSyncDate?: string; // ISO string
}
export interface StageDataItem {
  sales: number;
  recovery: number;
  notDistributed: number;
  nonSales: number;
  leads: {
    sales: Opportunity[];
    recovery: Opportunity[];
    notDistributed: Opportunity[];
    nonSales: Opportunity[];
  };
  originalStageName: string;
}
