import { toast } from 'react-toastify';

export const redistribuirLead = async (crmId: number | string) => {
  try {
    toast.info('Redistribuindo lead...');

    // Chama a nova API de redistribuição
    const response = await fetch(`https://api.leadhub.registrese.app.br/distribuir/redistribuir-lead/${crmId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('Falha ao redistribuir lead');
    }

    const data = await response.json();

    if (!data.message) {
      throw new Error('Resposta inválida da API');
    }

    // Mostra mensagem de sucesso com os detalhes da redistribuição
    toast.success(data.message);

    return {
      success: true,
      redistribuicao: data.redistribuicao
    };
  } catch (error) {
    console.error('Erro ao redistribuir lead:', error);
    toast.error('Erro ao redistribuir lead. Tente novamente.');
    throw error;
  }
};

// Mantém a função antiga para compatibilidade (deprecated)
export const reatribuirLead = redistribuirLead;
