import { toast } from 'react-toastify';

export const reatribuirLead = async (crmId: number | string) => {
  try {
    // Primeira requisição: atualiza o stage_id
    const response = await fetch(`/api/leads/reatribuir/${crmId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('Falha ao reatribuir lead');
    }

    const { success } = await response.json();
    
    if (!success) {
      throw new Error('Falha ao reatribuir lead');
    }

    toast.info('Lead enviado para reatribuição...');

    // Aguarda 5 segundos para dar tempo do backend processar a reatribuição
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Busca os dados atualizados do lead
    const updatedResponse = await fetch(`/api/leads/${crmId}`);

    if (!updatedResponse.ok) {
      throw new Error('Falha ao buscar dados atualizados do lead');
    }

    const { success: getSuccess, data: updatedData } = await updatedResponse.json();

    if (!getSuccess) {
      throw new Error('Falha ao buscar dados atualizados do lead');
    }
    
    if (updatedData.owner?.name) {
      toast.success(`Lead reatribuído para ${updatedData.owner.name}`);
    } else {
      toast.info('Lead em processo de reatribuição...');
    }

    return updatedData;
  } catch (error) {
    console.error('Erro ao reatribuir lead:', error);
    toast.error('Erro ao reatribuir lead. Tente novamente.');
    throw error;
  }
}; 
