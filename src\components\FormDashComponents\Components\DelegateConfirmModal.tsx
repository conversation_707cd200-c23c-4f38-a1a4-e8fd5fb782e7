import React from 'react';
import { FaExclamationTriangle } from 'react-icons/fa';

interface DelegateConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  fromVendedor: string;
  toVendedor: string;
  entradaLead: string;
}

const DelegateConfirmModal: React.FC<DelegateConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  fromVendedor,
  toVendedor,
  entradaLead
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-900/50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <FaExclamationTriangle className="text-yellow-500 text-2xl" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Confirmar Delegação
            </h3>
          </div>
          
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            O vendedor <span className="font-semibold">{fromVendedor}</span> já se apresentou 
            para este lead em <span className="font-semibold">{entradaLead}</span>.
          </p>
          
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Tem certeza que deseja delegar para <span className="font-semibold">{toVendedor}</span>?
          </p>

          <div className="flex justify-end gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              Cancelar
            </button>
            <button
              onClick={onConfirm}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-lg transition-colors"
            >
              Confirmar Delegação
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DelegateConfirmModal; 