import React, { ReactNode, useEffect, useRef } from "react";

interface SideModalProps {
  isOpen: boolean;
  onClose: () => void;
  header?: ReactNode;
  children: ReactNode;
}

const SideModal: React.FC<SideModalProps> = ({
  isOpen,
  onClose,
  header,
  children,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // Trancar o scroll do background quando o modal estiver aberto
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  return (
    <div
      ref={modalRef}
      className={`w-full h-full flex flex-col transform transition-transform duration-300 ease-in-out bg-white dark:bg-gray-900 ${
        isOpen ? "translate-x-0" : "translate-x-full"
      }`}
      role="dialog"
      aria-modal="true"
    >
      {/* Cabeçalho do Painel - Altura fixa */}
      <div className="flex items-center justify-between h-16 px-6 shrink-0 border-b border-gray-300 dark:border-gray-700">
        <div className="text-gray-800 dark:text-gray-200">
          {header}
        </div>
        <button
          onClick={onClose}
          className="flex items-center justify-center w-8 h-8 text-2xl text-gray-600 hover:text-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-400 rounded-full"
          aria-label="Fechar Modal"
        >
          &times;
        </button>
      </div>

      {/* Corpo do Painel - Altura flexível */}
      <div className="flex-1 overflow-hidden">
        {children}
      </div>
    </div>
  );
};

export default SideModal;
