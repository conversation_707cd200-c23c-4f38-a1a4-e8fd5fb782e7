import React, { useState } from "react";
import OpportunityCard from "./OpportunityCard";
import { format } from "date-fns"; // Para formatar e verificar a data
import { Opportunity } from "@/types/crm";


interface GroupedOpportunitiesProps {
  opportunities: Opportunity[];
}

const GroupedOpportunities: React.FC<GroupedOpportunitiesProps> = ({
  opportunities,
}) => {
  // Agrupamento por data
  const groupByDate = (items: Opportunity[]) => {
    return items.reduce((acc: { [key: string]: Opportunity[] }, item) => {
      const date = format(new Date(item.createdAt), "yyyy-MM-dd");
      if (!acc[date]) acc[date] = [];
      acc[date].push(item);
      return acc;
    }, {});
  };

  const groupedOpportunities: { [key: string]: Opportunity[] } = groupByDate(opportunities);
  const todayDateKey = format(new Date(), "yyyy-MM-dd");

  // Estado para controlar expansão por data
  const [expandedDates, setExpandedDates] = useState<string[]>([todayDateKey]);

  const toggleDateExpansion = (date: string) => {
    setExpandedDates((prev) =>
      prev.includes(date) ? prev.filter((d) => d !== date) : [...prev, date]
    );
  };

  return (
    <div>
      {Object.keys(groupedOpportunities).map((date) => (
        <div key={date} className="mb-6">
          <h3
            className="text-lg font-bold mb-4 cursor-pointer"
            onClick={() => toggleDateExpansion(date)}
          >
            {format(new Date(date), "dd/MM/yyyy")}
            <span className="ml-2">
              {expandedDates.includes(date) ? "🔽" : "🔼"}
            </span>
          </h3>

          {expandedDates.includes(date) && (
            <ul className="space-y-4">
              {groupedOpportunities[date].map((opp) => (
                <OpportunityCard
                  key={opp.id}
                  opportunity={opp}
                />
              ))}
            </ul>
          )}
        </div>
      ))}
    </div>
  );
};

export default GroupedOpportunities;
