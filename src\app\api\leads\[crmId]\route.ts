import { NextResponse } from 'next/server';

const CRM_TOKEN = process.env.CRM_TOKEN;
const CRM_API_URL = 'https://api.pipe.run/v1';

export async function GET(
  request: Request,
  { params }: { params: { crmId: string } }
) {
  try {
    const { crmId } = params;

    const response = await fetch(`${CRM_API_URL}/deals/${crmId}`, {
      headers: {
        'accept': 'application/json',
        'token': CRM_TOKEN || ''
      }
    });

    if (!response.ok) {
      throw new Error('Falha ao buscar dados do lead');
    }

    const data = await response.json();

    return NextResponse.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('Erro ao buscar dados do lead:', error);
    return NextResponse.json(
      { success: false, error: 'Erro ao buscar dados do lead' },
      { status: 500 }
    );
  }
} 