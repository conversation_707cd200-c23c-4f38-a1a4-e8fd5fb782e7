// components/Charts/ScoreChart.tsx
import React from "react";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

// Registrar os componentes do Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface ScoreChartProps {
  scores: number[];
}

const ScoreChart: React.FC<ScoreChartProps> = ({ scores }) => {
  // Contagem de formulários em cada faixa de score
  const scoreRanges = [
    "0-100",
    "101-110",
    "111-120",
    "121-130",
    "131-140",
    "141-160",
    "161-180",
    "180+",
  ];

  const scoreCounts = scoreRanges.map((range) => {
    const [min, max] = range.split("-").map(Number);
    return scores.filter((score) =>
      max ? score >= min && score <= max : score >= min
    ).length;
  });

  const data = {
    labels: scoreRanges,
    datasets: [
      {
        label: "Formulários por Score",
        data: scoreCounts,
        backgroundColor: "rgba(54, 162, 235, 0.6)",
        borderColor: "rgba(54, 162, 235, 1)",
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false, // Permite ajustar a altura
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: true,
        text: "Distribuição de Formulários por Score",
      },
    },
    scales: {
      x: {
        beginAtZero: true,
        ticks: {
          maxRotation: 0, // Mantém os labels alinhados
        },
      },
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1, // Incrementos mais ajustados para contagem baixa
        },
      },
    },
    layout: {
      padding: 10, // Adiciona um padding interno para reduzir a sensação de sobrecarga
    },
  };

  return (
    <div style={{ height: "300px" }}>
      {" "}
      {/* Limita a altura do gráfico */}
      <Bar data={data} options={options} />
    </div>
  );
};

export default ScoreChart;
