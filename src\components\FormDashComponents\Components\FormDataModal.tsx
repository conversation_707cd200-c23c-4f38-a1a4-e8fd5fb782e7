import React, { useMemo, useState, useEffect } from "react";
import {
  FaEnvelope,
  FaPhone,
  FaUser,
  FaTag,
  FaTimesCircle,
  FaStar,
  FaSquare,
  FaBan,
} from "react-icons/fa";
import SideModal from "./SideModal";
import { RecoveryButton } from "@/components/FormDashComponents/Components/RecoveryButton";
import ConfirmDeleteModal from "@/components/FormDashComponents/Components/DeleteConfirmModal";
import { questions } from "@/utils/questions";
import { toast } from "react-toastify";
import axios from "axios";
import { useFormDataModalStore } from "@/context/useFormDataModalStore";


interface FormDataType {
  [key: string]: string | number | boolean | string[] | null | undefined;
}

const isValidDDD = (ddd: string): boolean => {
  // Lista de DDDs válidos no Brasil
  const validDDDs = [
    '11', '12', '13', '14', '15', '16', '17', '18', '19', // SP
    '21', '22', '24', '27', '28', // RJ, ES
    '31', '32', '33', '34', '35', '37', '38', // MG
    '41', '42', '43', '44', '45', '46', '47', '48', '49', // PR, SC
    '51', '53', '54', '55', // RS
    '61', '62', '63', '64', '65', '66', '67', '68', '69', // DF, GO, TO, MT, RO, AC
    '71', '73', '74', '75', '77', '79', // BA, SE
    '81', '82', '83', '84', '85', '86', '87', '88', '89', // PE, AL, PB, RN, CE, PI
    '91', '92', '93', '94', '95', '96', '97', '98', '99'  // PA, AM, RR, AP, MA
  ];
  return validDDDs.includes(ddd);
};

const isValidPhone = (phone: string | undefined | null): boolean => {
  if (!phone) return false;

  // Remove todos os caracteres não numéricos
  const numericPhone = phone.replace(/\D/g, '');

  // Verifica se o número tem entre 10 e 11 dígitos
  if (numericPhone.length < 10 || numericPhone.length > 11) {
    return false;
  }

  // Extrai o DDD
  const ddd = numericPhone.substring(0, 2);
  
  // Verifica se o DDD é válido
  if (!isValidDDD(ddd)) {
    return false;
  }

  // Pega o número sem DDD
  const number = numericPhone.substring(2);

  // Verifica se é celular (9 dígitos) ou fixo (8 dígitos)
  if (number.length === 9) {
    // Para celular, primeiro dígito deve ser 9
    return number[0] === '9';
  } else if (number.length === 8) {
    // Para fixo, primeiro dígito deve ser entre 2 e 8
    const firstDigit = parseInt(number[0]);
    return firstDigit >= 2 && firstDigit <= 8;
  }

  return false;
};

const isValidEmail = async (email: string | undefined | null): Promise<boolean> => {
  if (!email) return false;

  try {
    const response = await fetch('/api/validate-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    const data = await response.json();
    return data.isValid;
  } catch (error) {
    console.error('Erro ao validar email:', error);
    // Em caso de erro na validação, usar a validação básica como fallback
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  }
};

const FormDataModal: React.FC = () => {
  const { selectedFormData, closeFormDataModal, isFormDataModalOpen } = useFormDataModalStore();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [progressPercentage, setProgressPercentage] = useState(0);
  const [isEmailValid, setIsEmailValid] = useState<boolean>(true);
  
  const parsedFormData = useMemo(() => {
    try {
      let data = typeof selectedFormData === "string" ? JSON.parse(selectedFormData) : selectedFormData;
      
      // Parse utmCampaign if it exists and is a string
      if (data.utmCampaign && typeof data.utmCampaign === 'string') {
        try {
          data.utmCampaign = JSON.parse(data.utmCampaign);
        } catch (e) {
          console.error('Erro ao parsear utmCampaign:', e);
        }
      }
      
      return data;
    } catch (error) {
      console.error("Erro ao parsear selectedFormData:", error);
      return {};
    }
  }, [selectedFormData]);

  const isGnBradda = useMemo(() => {
    const utmCampaign = parsedFormData.utmCampaign;
    if (Array.isArray(utmCampaign)) {
      return utmCampaign.includes('gn-bradda');
    }
    return false;
  }, [parsedFormData.utmCampaign]);

  const {
    crmId,
    leadName,
    leadMail,
    leadNumber,
    brandTitle,
    score,
  } = parsedFormData;

  useEffect(() => {
    const validateEmail = async () => {
      const email = parsedFormData.leadMail;
      if (email) {
        const isValid = await isValidEmail(email);
        setIsEmailValid(isValid);
      }
    };

    validateEmail();
  }, [parsedFormData.leadMail]);

  const hasInvalidContact = useMemo(() => {
    const phone = parsedFormData.leadNumber;
    return !isEmailValid || !isValidPhone(phone);
  }, [isEmailValid, parsedFormData.leadNumber]);

  // Calcular a porcentagem de preenchimento
  useEffect(() => {
    if (!parsedFormData) return;

    let answeredCount = 0;
    let totalCount = 0;

    for (const key of Object.keys(questions)) {
      const question = questions[key];
      const fieldName = question.name;
      totalCount++;

      const isAnswered =
        parsedFormData[fieldName] !== undefined &&
        parsedFormData[fieldName] !== "" &&
        parsedFormData[fieldName] !== null;

      if (isAnswered) {
        answeredCount++;
      }

      if (question.options) {
        const selectedOption = Object.values(question.options).find(
          (option) => option.answer === parsedFormData[fieldName]
        );

        if (selectedOption && selectedOption.subSelections) {
          totalCount++;
          const subFieldName = selectedOption.subSelections.name || "";
          const subIsAnswered =
            parsedFormData[subFieldName] !== undefined &&
            parsedFormData[subFieldName] !== "" &&
            parsedFormData[subFieldName] !== null;

          if (subIsAnswered) {
            answeredCount++;
          }
        }
      }
    }

    setProgressPercentage(
      parsedFormData.completed
        ? 100
        : totalCount > 0
        ? Math.round((answeredCount / totalCount) * 100)
        : 0
    );
  }, [parsedFormData]);

  // Função para tratar a recuperação
  const handleRecoverySuccess = () => {
    toast.success("Lead movido para recuperação com sucesso!", {
      toastId: `recovery-${crmId}`, // Garante que o toast seja único para cada lead
    });
  };
  // Função para calcular a cor do score
  const getScoreColor = (score: number | undefined) => {
    if (score === undefined) return "text-gray-400";
    
    // Vermelho (1-80)
    if (score <= 80) return "text-red-500";
    
    // Amarelo (81-160)
    if (score <= 160) return "text-yellow-500";
    
    // Azul claro (161-220)
    if (score <= 220) return "text-cyan-500";
    
    // Verde (221-300)
    if (score <= 300) return "text-green-500";
    
    // Azul (301+)
    return "text-blue-500";
  };

  // Função para renderizar perguntas específicas do formData
  const renderFormData = (parsedData: FormDataType) => {
    const result = [];

    for (const key of Object.keys(questions)) {
      const question = questions[key];
      const fieldName = question.name;
      const answer = parsedData[fieldName];

      if (
        ["leadName", "brandTitle", "leadMail", "leadNumber", "score"].includes(
          fieldName
        )
      )
        continue;

      if (answer !== undefined && answer !== null && answer !== "") {
        result.push({
          questionTitle: question.title,
          answer: Array.isArray(answer) ? answer.join(", ") : answer,
        });
      }

      if (question.options) {
        const selectedOption = Object.values(question.options).find(
          (option) => option.answer === answer
        );

        if (selectedOption?.subSelections) {
          const subFieldName = selectedOption.subSelections.name;
          const subAnswer = parsedData[subFieldName || ""];

          if (subAnswer && typeof subAnswer === "string") {
            result.push({
              questionTitle: selectedOption.subSelections.title,
              answer: subAnswer,
            });
          }
        }
      }
    }

    return result;
  };

  // Função para excluir a oportunidade
  const handleDeleteOpportunity = async () => {
    if (!crmId) return;
    try {
      const response = await axios.put(
        `https://api.form-dash.registrese.app.br/opportunities/delete/${crmId}`
      );
      if (response.status === 200 && response.data.success) {
        toast.success("Oportunidade excluída com sucesso!");
        closeFormDataModal(); 
      } else {
        throw new Error("Erro ao excluir oportunidade");
      }
    } catch (error) {
      console.error("Erro ao excluir oportunidade:", error);
      toast.error("Erro ao excluir oportunidade, tente novamente.");
    }
  };

  return (
    <SideModal
      isOpen={isFormDataModalOpen}
      onClose={closeFormDataModal}
      header={<h2 className="text-2xl font-bold">Detalhes do Cliente</h2>}
    >
      <div className="flex flex-col h-full overflow-hidden">
        {/* Container rolável para todo o conteúdo */}
        <div className="flex-1 overflow-y-auto">
          {/* Cabeçalho com Informações Principais */}
          <div className="flex flex-col bg-gray-100 dark:bg-gray-800 p-5 rounded-lg m-4 shadow">
            <div className="flex items-start justify-between mb-4">
              <div className="flex flex-col space-y-3">
                <h3 className="text-xl md:text-2xl font-bold text-gray-900 dark:text-gray-100 uppercase break-words max-w-[400px]">
                  {brandTitle || "Marca não informada"}
                </h3>
                <div className="text-gray-700 dark:text-gray-400 flex items-center">
                  <FaUser className="flex-shrink-0 mr-2" />
                  <span className="break-words">{leadName}</span>
                </div>
                <div className="text-gray-700 dark:text-gray-400 flex items-center">
                  <FaEnvelope className="flex-shrink-0 mr-2" />
                  <span className="break-words">{leadMail}</span>
                </div>
                <div className="text-gray-700 dark:text-gray-400 flex items-center">
                  <FaPhone className="flex-shrink-0 mr-2" />
                  <span className="break-words">{leadNumber}</span>
                </div>
              </div>
              <div className={`flex items-center text-xl ${getScoreColor(score)} flex-shrink-0 ml-4`}>
                {hasInvalidContact ? (
                  <div className="relative group">
                    <FaBan className="mr-2" />
                    <span className="absolute hidden group-hover:block bg-gray-800 text-white text-xs rounded py-1 px-2 -top-8 -right-2 whitespace-nowrap">
                      Contato inválido
                    </span>
                  </div>
                ) : isGnBradda ? (
                  <div className="relative group">
                    <FaSquare className="mr-2" />
                    <span className="absolute hidden group-hover:block bg-gray-800 text-white text-xs rounded py-1 px-2 -top-8 -right-2 whitespace-nowrap">
                      gn-bradda
                    </span>
                  </div>
                ) : (
                  <FaStar className="mr-2" />
                )}
                {score || "N/A"}
              </div>
            </div>

            {/* Barra de Progresso */}
            <div className="relative mt-4">
              <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded-full overflow-hidden">
                <div
                  className={`h-full transition-all duration-500 ${
                    progressPercentage < 50
                      ? "bg-red-500"
                      : progressPercentage < 80
                      ? "bg-yellow-500"
                      : "bg-green-500"
                  }`}
                  style={{ width: `${progressPercentage}%` }}
                ></div>
              </div>
              <div className="absolute inset-0 flex justify-center items-center">
                <span className="text-sm font-semibold text-gray-700 dark:text-gray-200">
                  {progressPercentage}%
                </span>
              </div>
            </div>
          </div>

          {/* Tabela de Informações do Formulário */}
          <div className="px-4 mb-20">
            <table className="w-full bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden border border-gray-200 dark:border-gray-700">
              <tbody>
                {renderFormData(parsedFormData).map((row, index) => (
                  <tr
                    key={index}
                    className={`${
                      index % 2 === 0
                        ? "bg-gray-50 dark:bg-gray-800"
                        : "bg-gray-100 dark:bg-gray-900"
                    } hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors`}
                  >
                    <td className="p-4 font-semibold text-gray-800 dark:text-gray-300 w-1/3">
                      {row.questionTitle}
                    </td>
                    <td className="p-4 text-gray-700 dark:text-gray-400 break-words">
                      {row.answer}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Botões de Ação - Fixo na parte inferior */}
        <div className="flex justify-between px-4 py-4 space-x-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 sticky bottom-0 shadow-lg">
          <div className="flex-1 max-w-[120px] relative">
            <RecoveryButton
              crmId={crmId}
              onRecoverySuccess={handleRecoverySuccess}
              isDisabled={hasInvalidContact || isGnBradda}
              reason={hasInvalidContact ? 'invalid-contact' : isGnBradda ? 'gn-bradda' : undefined}
            />
          </div>
          <button
            className="w-[120px] h-[38px] flex items-center justify-center px-4 bg-red-500 text-white text-sm rounded-md hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-400"
            onClick={() => setIsDeleteModalOpen(true)}
          >
            <FaTimesCircle className="mr-2" />
            Excluir
          </button>
        </div>

        {/* Confirm Delete Modal */}
        <ConfirmDeleteModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          onConfirm={handleDeleteOpportunity}
          opportunityName={brandTitle || "Oportunidade"}
        />
      </div>
    </SideModal>
  );
};

export default FormDataModal;
