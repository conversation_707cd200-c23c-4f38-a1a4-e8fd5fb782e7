/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */

import React, { useState } from "react";

// Mapeamento de chaves do JSON de inglês para português
export const keyMapping: { [key: string]: string } = {
  brandTitle: "Marca",
  leadName: "Nome",
  leadNumber: "Telefone",
  leadMail: "E-mail",
  businessInternship: "Estágio do Negócio",
  businessCollaborators: "Colaboradores",
  haveCNPJ: "Possui CNPJ",
  taxFramework: "Enquadramento Fiscal",
  notCnpjTaxFramework: "Enquadramento Fiscal",
  businessDefinition: "Definição do Negócio",
  clothingActing: "Atuação no Vestuário",
  clientSiteOrSocialLink: "Site ou Redes Sociais",
  reasonRegistration: "Motivo do Registro",
  degreeOfKnowledge: "Grau de Conhecimento",
  investmentInTheBrand: "Investimento na Marca",
  whenToStartProcess: "Quando deseja iniciar o processo",
  utmSource: "Fonte UTM",
  utmCampaign: "Campanha UTM",
  firstTimeUrl: "Primeira URL",
  score: "Score",
  firstVisitTime: "Primeira Visita",
  conversionTime: "Hora da Conversão",
  howClientFoundUs: "Como nos encontrou",
};

// Lista de campos que são datas e precisam de formatação
const dateFields = ["firstVisitTime", "conversionTime"];

// Função para formatar a data
export const formatDateTime = (dateString: Date | string) => {
  const date = new Date(dateString);
  return `${date.getDate().toString().padStart(2, "0")}/${(date.getMonth() + 1)
    .toString()
    .padStart(2, "0")}/${date.getFullYear()} ${date
    .getHours()
    .toString()
    .padStart(2, "0")}:${date.getMinutes().toString().padStart(2, "0")}:${date
    .getSeconds()
    .toString()
    .padStart(2, "0")}`;
};

interface LogDetailsProps {
  timestamp: Date | string;
  logMessage: string;
  darkMode?: boolean;
}

const LogDetails: React.FC<LogDetailsProps> = ({
  timestamp,
  logMessage,
  darkMode,
}) => {
  const [expanded, setExpanded] = useState(false);

  // Localizar o início do JSON na mensagem de log (normalmente começa com '{')
  const jsonStartIndex = logMessage.indexOf("{");
  const logPrefix =
    jsonStartIndex !== -1
      ? logMessage.slice(0, jsonStartIndex).trim()
      : logMessage;
  const jsonString =
    jsonStartIndex !== -1 ? logMessage.slice(jsonStartIndex) : "";

  // Verificar se existe uma string JSON e se é válida
  let parsedData: { [key: string]: any } = {};
  let isValidJson = false;

  if (jsonString && jsonString.trim()) {
    try {
      parsedData = JSON.parse(jsonString);
      isValidJson = true;
    } catch (error) {
      console.error("Erro ao fazer o parse do JSON", error);
    }
  }

  return (
    <div
      className={`p-4 rounded-md shadow-sm cursor-pointer transition-all duration-300 ease-in-out ${
        darkMode
          ? "bg-gray-800 text-gray-200 hover:bg-gray-700"
          : "bg-gray-100 text-gray-700 hover:bg-gray-300"
      }`}
      onClick={(e: any) => {
        // Apenas expandir/contrair o acordeão se o clique não for dentro de um elemento interativo
        if (!e.target.closest("table") && !e.target.closest("td")) {
          setExpanded(!expanded);
        }
      }}
    >
      <p className="text-sm">
        {formatDateTime(timestamp)} - {logPrefix}
      </p>

      {expanded && isValidJson && (
        <div className="mt-4">
          <table className="min-w-full table-auto border-collapse">
            <thead>
              <tr>
                <th
                  className={`border px-4 py-2 text-left ${
                    darkMode
                      ? "bg-gray-700 text-gray-300"
                      : "bg-gray-200 text-black"
                  }`}
                >
                  Campo
                </th>
                <th
                  className={`border px-4 py-2 text-left ${
                    darkMode
                      ? "bg-gray-700 text-gray-300"
                      : "bg-gray-200 text-black"
                  }`}
                >
                  Valor
                </th>
              </tr>
            </thead>

            <tbody>
              {Object.entries(parsedData).map(([key, value]) => (
                <tr
                  key={key}
                  className={`border px-4 py-2 transition-colors duration-300 ease-in-out ${
                    darkMode ? "hover:bg-gray-700" : "hover:bg-gray-100"
                  }`}
                >
                  <td className="border px-4 py-2 font-semibold">
                    {keyMapping[key] || key}
                  </td>
                  <td className="border px-4 py-2">
                    {dateFields.includes(key)
                      ? formatDateTime(value)
                      : Array.isArray(value)
                      ? value.join(", ")
                      : String(value)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {expanded && !isValidJson && (
        <p className="text-red-500 mt-2">Nenhum dado adicional disponível.</p>
      )}
    </div>
  );
};

export default LogDetails;
