import React from 'react';
import { FaRecycle, FaTag } from "react-icons/fa";
import axios from 'axios';
import { toast } from "react-toastify";

interface RecoveryButtonProps {
  crmId: string;
  onRecoverySuccess?: () => void;
  isMenuItem?: boolean;
  isDisabled?: boolean;
  reason?: 'invalid-contact' | 'gn-bradda';
}

export const RecoveryButton: React.FC<RecoveryButtonProps> = ({
  crmId,
  onRecoverySuccess,
  isMenuItem = false,
  isDisabled,
  reason
}) => {
  const handleRecovery = async () => {
    try {
      const response = await axios.put(
        `https://api.form-dash.registrese.app.br/opportunities/recovery/${crmId}`
      );

      if (response.status === 200 && response.data.success) {
        onRecoverySuccess?.();
      } else {
        throw new Error('Erro ao mover para recuperação');
      }
    } catch (error) {
      console.error('Erro ao mover para recuperação:', error);
      toast.error('Erro ao mover para recuperação, tente novamente.');
    }
  };

  const getDisabledReason = () => {
    switch (reason) {
      case 'invalid-contact':
        return 'Lead com contato inválido';
      case 'gn-bradda':
        return 'Lead gn-bradda';
      default:
        return 'Recuperação não permitida';
    }
  };

  if (isMenuItem) {
    return (
      <button
        disabled={isDisabled}
        onClick={() => !isDisabled && handleRecovery()}
        className={`w-full px-4 py-2 text-left text-sm flex items-center ${
          isDisabled 
            ? 'text-gray-400 cursor-not-allowed'
            : 'text-green-600 hover:bg-green-50 dark:hover:bg-green-900'
        }`}
      >
        <FaRecycle className="mr-2" />
        Recuperar
      </button>
    );
  }

  return (
    <div className="relative group">
      <button
        disabled={isDisabled}
        onClick={() => !isDisabled && handleRecovery()}
        className={`h-[38px] w-full flex items-center justify-center px-4 text-sm rounded-md transition-colors whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-green-400 ${
          isDisabled
            ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
            : 'bg-green-500 text-white hover:bg-green-600'
        }`}
      >
        <FaRecycle className="mr-1" />
        Recuperar
      </button>
      {isDisabled && (
        <div className="absolute hidden group-hover:block w-max bg-gray-800 text-white text-xs rounded py-1 px-2 -top-8 left-1/2 transform -translate-x-1/2 whitespace-nowrap">
          {getDisabledReason()}
        </div>
      )}
    </div>
  );
};
