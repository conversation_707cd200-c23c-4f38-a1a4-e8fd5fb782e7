import React, { useState, useMemo, useEffect, useCallback, useRef } from "react";
import { Opportunity } from "@/types/crm";
import FormDataModal from "./FormDataModal";
import { format, startOfMonth } from "date-fns";
import ListModalContent from "./ListModalContent";
import SideModal from "./SideModal";
import { FaCalendarAlt } from "react-icons/fa";
import axios from "axios";
import { groupFormsByDate } from "../DashUtilsTs/DatesUtils";
import FullCalendar from "./FullCalendar";
import { useCalendarStore } from "@/context/useReactStatesStore";
import { useFormDataModalStore } from "@/context/useFormDataModalStore";
import { DateRange } from "react-day-picker";

interface ParsedForm extends Omit<Opportunity, 'createdAt'> {
  createdAt: string;
  formData: {
    score?: number;
    [key: string]: unknown;
  };
}

const InProgressFormTabContent: React.FC = () => {
  const modalTitle = "Formulários em Progresso";
  // Estados para modais
  const { isFormDataModalOpen } = useFormDataModalStore();
  const [_formsCountByDate, setFormsCountByDate] = useState<
    Record<string, number>
  >({});

  const {
    inProgressForms,
    setInProgressForms,
    currentMonth,
    selectedDate,
    isListModalOpen,
    toggleListModal,
    isCalendarMinimized,
    setIsCalendarMinimized,
    setSelectedDate,
    setFormsGroupedByDate
  } = useCalendarStore();

  const [dateRange, setDateRange] = useState<DateRange | undefined>();

  // Efeito para controlar a minimização do calendário baseado no tamanho da tela
  useEffect(() => {
    const handleResize = () => {
      setIsCalendarMinimized(window.innerWidth <= 1500 && isListModalOpen);
    };

    handleResize(); // Verificar o tamanho inicial
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isListModalOpen, setIsCalendarMinimized]);

  const [isCalendarModalOpen, setIsCalendarModalOpen] = useState(false);
  const calendarContentRef = useRef<HTMLDivElement>(null);
  const toggleCalendarModal = () => {
    setIsCalendarModalOpen((prev) => !prev);
  };
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (
      calendarContentRef.current &&
      !calendarContentRef.current.contains(e.target as Node)
    ) {
      toggleCalendarModal();
    }
  };
  // Estados para filtros
  const [scoreFilter, setScoreFilter] = useState<{
    min: number | "";
    max: number | "";
  }>({ min: "", max: "" });
  const [formProgressFilter, setFormProgressFilter] = useState("");
  const [brandTitleFilter, setBrandTitleFilter] = useState("");

  // Função para buscar oportunidades em progresso do backend
  type ApiResponse = Record<string, Opportunity[]>;

  const fetchInProgressOpportunities = useCallback(async () => {
    const createdAtStart = `${format(startOfMonth(currentMonth), "yyyy-MM-dd")}`;
    const endpoint = `https://api.form-dash.registrese.app.br/opportunities/getInProgressOpportunities?created_at_start=${createdAtStart}`;

    try {
      const response = await axios.get<{ success: boolean; data: ApiResponse }>(
        endpoint
      );
      const data = response.data.data;
      
      // Cria um objeto com a contagem de itens por data
      const formsCountByDate = Object.entries(data).reduce(
        (acc, [date, forms]) => {
          acc[date] = Array.isArray(forms) ? forms.length : 0;
          return acc;
        },
        {} as Record<string, number>
      );

      setFormsCountByDate(formsCountByDate);

      const parsedForms: ParsedForm[] = Object.entries(data).flatMap(
        ([_date, forms]) =>
          (Array.isArray(forms) ? forms : []).map((form) => ({
            ...form,
            createdAt: form.createdAt,
            formData:
              typeof form.formData === "string"
                ? JSON.parse(form.formData)
                : form.formData,
          }))
      );
      
      setInProgressForms(parsedForms);
      
      // Agrupar os formulários por data e atualizar o store
      const groupedForms = groupFormsByDate(parsedForms, currentMonth);
      setFormsGroupedByDate(groupedForms);
      
    } catch (error) {
      console.error("Erro ao buscar oportunidades:", error);
    }
  }, [currentMonth, setInProgressForms, setFormsGroupedByDate]);

  // Chamada ao buscar oportunidades quando o componente for montado ou o mês mudar
  useEffect(() => {
    fetchInProgressOpportunities();
  }, [fetchInProgressOpportunities]);
  // Obter valores únicos para formProgress e brandTitle
  const uniqueFormProgressValues = useMemo(() => {
    const progressSet = new Set<string>();
    inProgressForms.forEach((opp) => {
      if (opp.formProgress) progressSet.add(opp.formProgress);
    });
    return Array.from(progressSet);
  }, [inProgressForms]);

  const uniqueBrandTitles = useMemo(() => {
    const brandSet = new Set<string>();
    inProgressForms.forEach((opp) => {
      if (opp.brandTitle) brandSet.add(opp.brandTitle);
    });
    return Array.from(brandSet);
  }, [inProgressForms]);

  // Aplicar filtros
  const getFilteredFormsForDate = () => {
    if (!selectedDate && !dateRange?.from) return [];

    let filteredForms = [...inProgressForms];

    // Filtrar por intervalo de datas
    if (dateRange?.from) {
      const fromDate = new Date(dateRange.from);
      fromDate.setHours(0, 0, 0, 0);
      
      filteredForms = filteredForms.filter((opp) => {
        const oppDate = new Date(opp.createdAt);
        oppDate.setHours(0, 0, 0, 0);
        
        if (dateRange.to) {
          const toDate = new Date(dateRange.to);
          toDate.setHours(23, 59, 59, 999);
          return oppDate >= fromDate && oppDate <= toDate;
        }
        return oppDate.getTime() === fromDate.getTime();
      });
    } else if (selectedDate) {
      // Filtrar por data única
      const dateKey = format(selectedDate, "yyyy-MM-dd");
      filteredForms = groupFormsByDate(inProgressForms, currentMonth)[dateKey] || [];
    }

    // Aplicar outros filtros
    if (brandTitleFilter) {
      filteredForms = filteredForms.filter((opp) =>
        opp.brandTitle?.toLowerCase().includes(brandTitleFilter.toLowerCase())
      );
    }

    if (scoreFilter.min !== "" || scoreFilter.max !== "") {
      filteredForms = filteredForms.filter((opp) => {
        const score =
          typeof opp.formData === "string"
            ? JSON.parse(opp.formData)?.score
            : opp.formData?.score;
        return (
          (scoreFilter.min === "" || score >= Number(scoreFilter.min)) &&
          (scoreFilter.max === "" || score <= Number(scoreFilter.max))
        );
      });
    }

    if (formProgressFilter) {
      filteredForms = filteredForms.filter(
        (opp) => opp.formProgress === formProgressFilter
      );
    }

    return filteredForms;
  };

  // Atribuir os formulários filtrados para uso direto
  const filteredFormsForDate = getFilteredFormsForDate();

 

  return (
    <div className="flex flex-row h-screen gap-4 overflow-hidden">
      {/* Calendário */}
      {!isFormDataModalOpen && !isCalendarMinimized && (
        <div
          className={`transition-all duration-300 ease-in-out p-4 bg-gray-50 dark:bg-gray-800 rounded-lg w-full max-w-[800px] h-full overflow-auto`}
        >
          <div className="flex flex-col md:flex-row items-center justify-between mb-6 w-full">
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200">
              Entrada - Form Incompleto{" "}
              <FaCalendarAlt className="inline ml-2" />
            </h2>
          </div>
          <div className="w-full">
            <FullCalendar />
          </div>
        </div>
      )}

      {(isFormDataModalOpen || isCalendarMinimized) && (
        <button
          onClick={toggleCalendarModal}
          className="fixed bottom-24 left-3 p-4 bg-blue-500 text-white rounded-full shadow-lg hover:bg-blue-600 transition-colors z-10"
        >
          <FaCalendarAlt size={24} />
        </button>
      )}

      {isCalendarModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={handleBackdropClick}
        >
          <div
            className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-3xl w-full max-h-[90vh] overflow-auto"
            ref={calendarContentRef}
          >
            <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
              Calendário
            </h2>
            <FullCalendar />
          </div>
        </div>
      )}
      {/* Container para os modais */}
      <div className="flex flex-1 gap-4 min-w-0 h-full">
        {/* SideModal para listar os formulários */}
        {isListModalOpen && (
          <div
            className={`transition-all duration-300 ease-in-out bg-white dark:bg-gray-900 shadow-lg h-full ${
              isFormDataModalOpen 
                ? 'w-1/2' 
                : 'flex-1'
            }`}
          >
            <SideModal
              isOpen={isListModalOpen}
              onClose={() => {
                toggleListModal();
                if (window.innerWidth <= 1500) {
                  setIsCalendarMinimized(false);
                }
              }}
              header={<h2 className="text-xl font-semibold">{modalTitle}</h2>}
            >
              <ListModalContent
                _totalForms={inProgressForms.length}
                filteredForms={filteredFormsForDate}
                scoreFilter={scoreFilter}
                setScoreFilter={setScoreFilter}
                formProgressFilter={formProgressFilter}
                setFormProgressFilter={setFormProgressFilter}
                uniqueFormProgressValues={uniqueFormProgressValues}
                uniqueBrandTitles={uniqueBrandTitles}
                brandTitleFilter={brandTitleFilter}
                setBrandTitleFilter={setBrandTitleFilter}
                onUpdate={fetchInProgressOpportunities}
                _isOpenFormDataModal={isFormDataModalOpen}
                selectedDate={selectedDate || new Date()}
                onDateChange={(date) => {
                  setSelectedDate(date);
                  setDateRange(undefined);
                  if (!isListModalOpen) {
                    toggleListModal();
                  }
                }}
                dateRange={dateRange}
                onDateRangeChange={(range) => {
                  setDateRange(range);
                  if (range?.from) {
                    setSelectedDate(range.from);
                  }
                  if (!isListModalOpen) {
                    toggleListModal();
                  }
                }}
              />
            </SideModal>
          </div>
        )}

        {/* FormDataModal para exibir os detalhes */}
        {isFormDataModalOpen && (
          <div className="transition-all duration-300 ease-in-out bg-white dark:bg-gray-900 shadow-lg w-1/2 h-full overflow-auto">
            <FormDataModal />
          </div>
        )}
      </div>
    </div>
  );
};

export default InProgressFormTabContent;
