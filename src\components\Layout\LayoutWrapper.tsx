"use client"; // Agora este componente pode usar hooks como useEffect

import { useEffect, useState } from "react";
import Layout from "@/components/Layout/Layout";

export default function LayoutWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) return null; // Evitar problemas de hidratação

  return <Layout>{children}</Layout>;
}
