import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: ["class"],
  theme: {
    minHeight: {
      containerVerde: "21.3125rem",
    },
    extend: {
      animation: {
        "ping-fast": "ping 500ms cubic-bezier(0, 0, 0.2, 1) ",
        "fade-in": "fadeIn 0.3s ease-in-out",
        "scale-in": "scaleIn 0.3s ease-in-out",
      },
      keyframes: {
        fadeIn: {
          "0%": {
            opacity: "0",
          },
          "100%": {
            opacity: "1",
          },
        },
        scaleIn: {
          "0%": {
            transform: "scale(0)",
          },
          "100%": {
            transform: "scale(1)",
          },
        },
      },
      spacing: {
        maxHeightSection: "49.375rem",
      },
      boxShadow: {
        custom: "0 2.1875rem 1.875rem rgba(94, 187, 71, 0.18)",
      },
      colors: {
        "custom-purple": "rgba(217, 210, 233, 0.4)",
        "custom-cyan": "rgba(0, 255, 255, 0.4)",
        "custom-green": "rgba(217, 234, 211, 0.4)",
        "custom-yellow": "rgba(255, 242, 204, 0.4)",
        brancoHeader: "rgba(255,255,255,0.6)",
        verde: "#30B946",
        verdeOpaco: "#5EBB47",
        verdeClaro: "#38D658",
        verdeEscuro: "#2FA43A",
        azul: "#269AB6",
        azulHover: "#2CB1D1",
        azulClaro: "#30C1E0",
        azulEscuro: "#2090A8",
        TextosEscurosEBg: "#1C3622",
        backgroundCinza: "#EFEFEF",
        branco: "#FFFFFF",
        preto: "#000000",
        cinza: "#7C7D7C",
        cinzaClaro: "#E6E6E6",
        cinzaFooter: "#808080",
        cinzaFaixa: "#D9D9D9",
        cinzaTexto: "#7D7D7D",
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        lineGray: "#D8D8D8",
        verdeDark: "#277A34",
        azulDark: "#1D708C",
        cinzaDark: "#333333",
        brancoDark: "#F1F1F1",
        darkIncomplete: "#FF5722",
        darkComplete: "#26A69A",
        eleganteLight: {
          primary: "#4A90E2",
          secondary: "#50E3C2",
          background: "#F5F7FA",
          border: "#D1D5DB",
        },
        eleganteDark: {
          primary: "#63B3ED",
          secondary: "#38B2AC",
          background: "#1A202C",
          border: "#4A5568",
        },
        produtivoLight: {
          sales: "#FF5733",
          notDistributed: "#FFC300",
          nonSales: "#C70039",
          recovery: "#28B463",
        },
        produtivoDark: {
          sales: "#FF8C42",
          notDistributed: "#FFD700",
          nonSales: "#FF4D4D",
          recovery: "#7DCEA0",
        },
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
      },
      screens: {
        "custom-sm": "480px",
        "custom-md": "768px",
        "custom-lg": "1024px",
        "custom-y": "1268px",
        "custom-x": "1368px",
        "custom-xl": "1440px",
        "custom-xly": "1600px",
        "custom-xxl": "2440px",
      },
      fontFamily: {
        custom: ["Raleway"],
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
export default config;
