import React from "react";
import LogDetails from "./LogDetails";
import { Log } from "@/types/crm";


interface LogListProps {
  logs: Log[];
  darkMode?: boolean;
}

const LogList: React.FC<LogListProps> = ({ logs, darkMode }) => {
    return (
      <ul
        className={`ml-4 mt-2 space-y-2 ${
          darkMode ? "text-gray-200" : "text-gray-700"
        }`}
      >
        {logs.map((log) => (
          <li key={log.id}>
            <LogDetails
              timestamp={log.timestamp}
              logMessage={log.log}
              darkMode={darkMode}
            />
          </li>
        ))}
      </ul>
    );
};

export default LogList;
