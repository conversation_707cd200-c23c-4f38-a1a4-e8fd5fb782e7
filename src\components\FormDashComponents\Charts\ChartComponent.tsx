// components/Charts/InitialChart.tsx
import React from "react";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  TooltipItem,
} from "chart.js";

// Registrar os componentes do Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface InitialChartProps {
  data: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      backgroundColor: string;
    }[];
  };
}

const InitialChart: React.FC<InitialChartProps> = ({ data }) => {
const options = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: "top" as const,
    },
    tooltip: {
      callbacks: {
        label: function (tooltipItem: TooltipItem<"bar">) {
          const datasetLabel = tooltipItem.dataset.label || ""; // Tipado como string | undefined, por isso usamos || ''
          const currentValue = tooltipItem.raw as number; // Tipar o valor bruto como number
          const dataIndex = tooltipItem.dataIndex; // dataIndex já é um número

          // Obtém o valor correspondente ao dataset "Completos"
          const completedValue = tooltipItem.chart.data.datasets[1].data[
            dataIndex
          ] as number; // Garantir que o valor do dataset seja número
          const total = currentValue + completedValue;

          // Verifica se o tooltip atual é para "Formulários Incompletos"
          if (datasetLabel === "Formulários Incompletos") {
            const percentage =
              total > 0 ? ((currentValue / total) * 100).toFixed(2) : "0.00";
            return `${datasetLabel}: ${currentValue} (${percentage}%)`;
          }

          // Para "Formulários Completos", retorna o valor sem porcentagem
          return `${datasetLabel}: ${currentValue}`;
        },
      },
    },
  },
  scales: {
    x: {
      stacked: true,
    },
    y: {
      beginAtZero: true,
      stacked: true,
      ticks: {
        precision: 0,
      },
    },
  },
};

  return <Bar data={data} options={options} />;
};

export default InitialChart;
