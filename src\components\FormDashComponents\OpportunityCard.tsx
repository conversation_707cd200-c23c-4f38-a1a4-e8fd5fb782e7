import React, { useMemo, useState, useEffect, useRef } from "react";
import {
  FaTimesCircle,
  FaEye,
  FaEyeSlash,
  FaHourglassHalf,
  FaStar,
  FaCheck,
  FaTimes,
  FaTag,
  FaEllipsisV,
  FaSquare,
  FaBan,
} from "react-icons/fa";
import Link from "next/link";
import { Opportunity } from "@/types/crm";

import axios from "axios";
import { toast } from "react-toastify";
import { questions } from "@/utils/questions";
import {
  parseISO,
  differenceInMinutes,
  differenceInHours,
  differenceInDays,
} from "date-fns";
import ConfirmDeleteModal from "./Components/DeleteConfirmModal";
import { useFormDataModalStore } from "@/context/useFormDataModalStore";
import { RecoveryButton } from "./Components/RecoveryButton";

export interface OpportunityCardProps {
  opportunity: Opportunity;
  page?: string;
  onUpdate?: () => void; // Callback para atualizar oportunidades
}

interface ProgressDetail {
  title: string;
  isAnswered: boolean;
}

interface ParsedFormData {
  [key: string]: string | number | boolean | null | undefined | any;
}

const formatTimeDifference = (updatedAt: string): string => {
  const now = new Date();
  const lastUpdate = parseISO(updatedAt);

  const diffInMinutesTotal = differenceInMinutes(now, lastUpdate);

  // Caso o valor seja negativo ou igual a -1
  if (diffInMinutesTotal <= 0) {
    return "há menos de 1 minuto";
  }

  if (diffInMinutesTotal === 1) {
    return "há 1 minuto";
  } else if (diffInMinutesTotal < 60) {
    return `há ${diffInMinutesTotal} minutos`;
  }

  const hours = differenceInHours(now, lastUpdate);
  if (hours < 24) {
    const minutes = diffInMinutesTotal % 60;
    return `há ${hours} hora${hours !== 1 ? "s" : ""} e ${minutes} minuto${
      minutes !== 1 ? "s" : ""
    }`;
  }

  const days = differenceInDays(now, lastUpdate);
  const hoursInDay = differenceInHours(now, lastUpdate) % 24;
  return `há ${days} dia${days !== 1 ? "s" : ""} e ${hoursInDay} hora${
    hoursInDay !== 1 ? "s" : ""
  }`;
};

// Função para definir a cor do ribbon com base no businessInternship
const getRibbonColor = (businessInternship: string): string => {
  switch (businessInternship) {
    case "Já atuo há mais de 2 anos":
      return "#3B82F6"; // bg-blue-500
    case "Já atuo há mais de 6 meses":
      return "#07a8a8"; // cyan
    case "Comecei recentemente":
      return "#22C55E"; // verde do botão recuperar
    case "Pretendo começar em breve":
      return "#4B5563"; // cinza mais escuro
    case "Por enquanto é só um plano":
    default:
      return "#F3F4F6"; // cor do card
  }
};

// Função para calcular a cor do score
const getScoreColor = (score: number | undefined) => {
  if (score === undefined) return "text-gray-400";
  
  // Vermelho (1-80)
  if (score <= 80) return "text-red-500";
  
  // Amarelo (81-160)
  if (score <= 160) return "text-yellow-500";
  
  // Azul claro (161-220)
  if (score <= 220) return "text-cyan-500";
  
  // Verde (221-300)
  if (score <= 300) return "text-green-500";
  
  // Azul (301+)
  return "text-blue-500";
};

const isValidDDD = (ddd: string): boolean => {
  // Lista de DDDs válidos no Brasil
  const validDDDs = [
    '11', '12', '13', '14', '15', '16', '17', '18', '19', // SP
    '21', '22', '24', '27', '28', // RJ, ES
    '31', '32', '33', '34', '35', '37', '38', // MG
    '41', '42', '43', '44', '45', '46', '47', '48', '49', // PR, SC
    '51', '53', '54', '55', // RS
    '61', '62', '63', '64', '65', '66', '67', '68', '69', // DF, GO, TO, MT, RO, AC
    '71', '73', '74', '75', '77', '79', // BA, SE
    '81', '82', '83', '84', '85', '86', '87', '88', '89', // PE, AL, PB, RN, CE, PI
    '91', '92', '93', '94', '95', '96', '97', '98', '99'  // PA, AM, RR, AP, MA
  ];
  return validDDDs.includes(ddd);
};

const isValidPhone = (phone: string | undefined | null): boolean => {
  if (!phone) return false;

  // Remove todos os caracteres não numéricos
  const numericPhone = phone.replace(/\D/g, '');

  // Verifica se o número tem entre 10 e 11 dígitos
  if (numericPhone.length < 10 || numericPhone.length > 11) {
    return false;
  }

  // Extrai o DDD
  const ddd = numericPhone.substring(0, 2);
  
  // Verifica se o DDD é válido
  if (!isValidDDD(ddd)) {
    return false;
  }

  // Pega o número sem DDD
  const number = numericPhone.substring(2);

  // Verifica se é celular (9 dígitos) ou fixo (8 dígitos)
  if (number.length === 9) {
    // Para celular, primeiro dígito deve ser 9
    return number[0] === '9';
  } else if (number.length === 8) {
    // Para fixo, primeiro dígito deve ser entre 2 e 8
    const firstDigit = parseInt(number[0]);
    return firstDigit >= 2 && firstDigit <= 8;
  }

  return false;
};

const isValidEmail = async (email: string | undefined | null): Promise<boolean> => {
  if (!email) return false;

  try {
    const response = await fetch('/api/validate-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    const data = await response.json();
    return data.isValid;
  } catch (error) {
    console.error('Erro ao validar email:', error);
    // Em caso de erro na validação, usar a validação básica como fallback
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  }
};

const OpportunityCard: React.FC<OpportunityCardProps> = ({
  opportunity,
  page,
  onUpdate,
}) => {
  console.log(opportunity)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isContentVisible, setIsContentVisible] = useState(false);
  const [progressDetails, setProgressDetails] = useState<ProgressDetail[]>([]);
  const [progressPercentage, setProgressPercentage] = useState(0);
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  const { openFormDataModal, setSelectedFormData } = useFormDataModalStore();

  const [isEmailValid, setIsEmailValid] = useState<boolean>(true);

  const parsedFormData: ParsedFormData = useMemo(() => {
    if (!opportunity || !opportunity.formData) return {};

    try {
      let formData = opportunity.formData;

      if (typeof formData === "string") {
        formData = JSON.parse(formData);
      }

      // Parse utmCampaign if it exists and is a string
      if (formData.utmCampaign && typeof formData.utmCampaign === 'string') {
        try {
          formData.utmCampaign = JSON.parse(formData.utmCampaign);
        } catch (e) {
          console.error('Erro ao parsear utmCampaign:', e);
        }
      }

      const parsedData: ParsedFormData = {
        ...formData,
        crmId: opportunity.crmId,
        inRecovery: opportunity.inRecovery || opportunity.stageId === "506789",
        stageId: opportunity.stageId,
        stageName: opportunity.stageName,
      };

      return parsedData;
    } catch (error) {
      console.error("Erro ao parsear formData:", error);
      return {};
    }
  }, [opportunity]);

  const getLastQuestionInfo = (parsedData: ParsedFormData) => {
    let lastQuestionTitle = "Nenhuma pergunta respondida";
    let answer = "";

    for (const key of Object.keys(questions)) {
      const question = questions[key];
      const fieldName = question.name;

      if (
        parsedData[fieldName] !== undefined &&
        parsedData[fieldName] !== "" &&
        parsedData[fieldName] !== null
      ) {
        lastQuestionTitle = question.title;
        answer = String(parsedData[fieldName]);

        const selectedOption = question.options
          ? Object.values(question.options).find(
              (option) => option.answer === parsedData[fieldName]
            )
          : null;

        if (
          selectedOption &&
          selectedOption.subSelections &&
          parsedData[selectedOption.subSelections.name || ""]
        ) {
          lastQuestionTitle =
            selectedOption.subSelections.title || lastQuestionTitle;
          answer = String(parsedData[selectedOption.subSelections.name || ""]);
        }
      } else {
        break;
      }
    }

    return { lastQuestionTitle, answer };
  };

  const { lastQuestionTitle, answer } = useMemo(
    () => getLastQuestionInfo(parsedFormData),
    [parsedFormData]
  );

  useEffect(() => {
    if (!opportunity) return;

    const details: any[] = [];
    let answeredCount = 0;
    let totalCount = 0;

    for (const key of Object.keys(questions)) {
      const question = questions[key];
      const fieldName = question.name;
      totalCount++;

      const isAnswered =
        parsedFormData[fieldName] !== undefined &&
        parsedFormData[fieldName] !== "" &&
        parsedFormData[fieldName] !== null;

      if (isAnswered) {
        answeredCount++;
      }

      details.push({
        title: question.title,
        isAnswered,
      });

      if (question.options) {
        const selectedOption = Object.values(question.options).find(
          (option) => option.answer === parsedFormData[fieldName]
        );

        if (selectedOption && selectedOption.subSelections) {
          totalCount++;
          const subFieldName = selectedOption.subSelections.name || "";
          const subIsAnswered =
            parsedFormData[subFieldName] !== undefined &&
            parsedFormData[subFieldName] !== "" &&
            parsedFormData[subFieldName] !== null;

          if (subIsAnswered) {
            answeredCount++;
          }

          details.push({
            title: selectedOption.subSelections.title || "",
            isAnswered: subIsAnswered,
          });
        }
      }
    }

    setProgressDetails(details);
    setProgressPercentage(
      opportunity.completed
        ? 100
        : totalCount > 0
        ? Math.round((answeredCount / totalCount) * 100)
        : 0
    );
  }, [parsedFormData, opportunity]);

  useEffect(() => {
    const validateEmail = async () => {
      const email = parsedFormData.leadMail;
      if (email) {
        const isValid = await isValidEmail(email);
        setIsEmailValid(isValid);
      }
    };

    validateEmail();
  }, [parsedFormData.leadMail]);

  // Fechar o menu quando clicar fora dele
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleDeleteOpportunity = async () => {
    if (!opportunity) return;
    const { crmId } = opportunity;

    try {
      const response = await axios.put(
        `https://api.form-dash.registrese.app.br/opportunities/delete/${crmId}`
      );
      if (response.status === 200 && response.data.success) {
        toast.success("Oportunidade excluída com sucesso!");
        if (onUpdate) {
          // Atualiza a lista de oportunidades sem fechar o modal
          await onUpdate();
        }
      } else {
        throw new Error("Erro ao excluir oportunidade");
      }
    } catch (error) {
      console.error("Erro ao excluir oportunidade:", error);
      toast.error("Erro ao excluir oportunidade, tente novamente.");
    }
  };

  const timeSinceUpdate = useMemo(() => {
    // Verifica se há logs e obtém o último timestamp
    const latestLogTimestamp =
      opportunity.Log && opportunity.Log.length > 0
        ? opportunity.Log[opportunity.Log.length - 1].timestamp
        : null;

    // Usa o último timestamp do log, ou `updatedAt` como fallback
    const lastUpdate = latestLogTimestamp || opportunity.updatedAt;
    if (!lastUpdate) return null;

    // Formata a diferença de tempo
    return formatTimeDifference(lastUpdate.toString());
  }, [opportunity.Log, opportunity.updatedAt]);

  const handleRecoverySuccess = () => {
    toast.success("Lead movido para recuperação com sucesso!", {
      toastId: `recovery-${opportunity.crmId}`, // Garante que o toast seja único para cada lead
    });
  };

  const isGnBradda = useMemo(() => {
    const utmCampaign = parsedFormData.utmCampaign;
    if (Array.isArray(utmCampaign)) {
      return utmCampaign.includes('gn-bradda');
    }
    return false;
  }, [parsedFormData.utmCampaign]);

  const hasInvalidContact = useMemo(() => {
    const phone = parsedFormData.leadNumber;
    return !isEmailValid || !isValidPhone(phone);
  }, [isEmailValid, parsedFormData.leadNumber]);

  if (!opportunity || opportunity.deleted || opportunity.status === 3)
    return null;

  const { crmId, inRecovery } = opportunity;
  const score = parsedFormData.score ? Number(parsedFormData.score) : undefined;
  const businessInternship = parsedFormData.businessInternship as string;

  // Cor do ribbon com base no businessInternship
  const ribbonColor = businessInternship ? getRibbonColor(businessInternship) : "#D9D2E9";

  return (
    <>
      <div className="relative flex flex-col justify-between p-5 bg-gray-100 dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-shadow h-[500px] w-[420px]">
        {/* Ribbon */}
        <div
          className="absolute top-0 left-0 h-full"
          style={{
            width: "8px",
            backgroundColor: ribbonColor,
            borderRadius: "8px 0 0 8px",
          }}
        ></div>

        {/* Conteúdo do Card */}
        <div className="space-y-4 flex-grow pl-3">
          {/* Título da Marca */}
          <div className="flex items-center justify-between">
            <h3
              className="text-xl font-bold text-gray-900 dark:text-gray-100 uppercase truncate max-w-[300px] hover:tooltip"
              title={
                opportunity?.formData?.brandTitle || "Marca não disponível"
              }
            >
              {opportunity?.formData?.brandTitle || "Marca não disponível"}
            </h3>
            {score !== undefined && (
              <div
                className={`flex items-center font-semibold text-lg ml-2 ${getScoreColor(score)}`}
              >
                {hasInvalidContact ? (
                  <div className="relative group">
                    <FaBan className="mr-1" />
                    <span className="absolute hidden group-hover:block bg-gray-800 text-white text-xs rounded py-1 px-2 -top-8 -right-2 whitespace-nowrap">
                      Contato inválido
                    </span>
                  </div>
                ) : isGnBradda ? (
                  <div className="relative group">
                    <FaSquare className="mr-1" />
                    <span className="absolute hidden group-hover:block bg-gray-800 text-white text-xs rounded py-1 px-2 -top-8 -right-2 whitespace-nowrap">
                      gn-bradda
                    </span>
                  </div>
                ) : (
                  <FaStar className="mr-1" />
                )}
                {score}
              </div>
            )}
          </div>

          {/* Tabela de Informações */}
          <div className="overflow-x-auto">
            <table className="w-full table-auto border-collapse">
              <tbody>
                <tr>
                  <td className="py-2 pr-4 font-semibold text-gray-700 dark:text-gray-300 w-[140px]">
                    Nome:
                  </td>
                  <td className="py-2 text-gray-600 dark:text-gray-400">
                    {parsedFormData.leadName ||
                      opportunity.leadName ||
                      "Nome não informado"}
                  </td>
                </tr>
                <tr>
                  <td className="py-2 pr-4 font-semibold text-gray-700 dark:text-gray-300 w-[140px]">
                    Telefone:
                  </td>
                  <td className="py-2 text-gray-600 dark:text-gray-400">
                    {parsedFormData.leadNumber ||
                      opportunity.leadNumber ||
                      "Telefone não informado"}
                  </td>
                </tr>
                <tr>
                  <td className="py-2 pr-4 font-semibold text-gray-700 dark:text-gray-300 w-[140px]">
                    CRM:
                  </td>
                  <td className="py-2">
                    <Link
                      href={`https://app.pipe.run/pipeline/gerenciador/visualizar/${crmId}`}
                      target="_blank"
                      className="text-blue-500 dark:text-blue-400 underline hover:text-blue-700"
                    >
                      {crmId}
                    </Link>
                  </td>
                </tr>
                {/* Última Pergunta */}
                {!opportunity.completed && (
                  <>
                    <tr>
                      <td className="py-2 pr-4 font-semibold text-gray-700 dark:text-gray-300 w-[140px]">
                        Última Pergunta:
                      </td>
                      <td className="py-2 flex items-center text-gray-800 dark:text-gray-200">
                        <span className="flex-grow">{lastQuestionTitle}</span>
                        {answer && (
                          <button
                            className="ml-2 text-gray-800 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 focus:outline-none flex-shrink-0"
                            onClick={() =>
                              setIsContentVisible(!isContentVisible)
                            }
                          >
                            {isContentVisible ? <FaEyeSlash /> : <FaEye />}
                          </button>
                        )}
                      </td>
                    </tr>
                  </>
                )}
                {opportunity.completed && (
                  <>
                    {/* Dono */}
                    {opportunity.ownerName && (
                      <tr>
                        <td className="py-2 pr-4 font-semibold text-gray-700 dark:text-gray-300 w-[140px]">
                          Dono:
                        </td>
                        <td className="py-2 text-gray-600 dark:text-gray-400">
                          {opportunity.ownerName}
                        </td>
                      </tr>
                    )}
                    {/* Etapa */}
                    {opportunity.stageName && (
                      <tr>
                        <td className="py-2 pr-4 font-semibold text-gray-700 dark:text-gray-300 w-[140px]">
                          Etapa:
                        </td>
                        <td className="py-2 text-gray-600 dark:text-gray-400">
                          {opportunity.stageName}
                        </td>
                      </tr>
                    )}
                  </>
                )}
                {isContentVisible && answer && (
                  <tr>
                    <td className="py-2 pr-4 font-semibold text-gray-700 dark:text-gray-300 w-[140px]">
                      Resposta:
                    </td>
                    <td className="py-2 text-black dark:text-gray-400">
                      {answer}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
            {timeSinceUpdate && !opportunity.completed && (
              <span className="mt-3 text-gray-500 dark:text-gray-400 text-sm flex items-center justify-end">
                <FaHourglassHalf className="mr-2" />
                {timeSinceUpdate}
              </span>
            )}
          </div>
        </div>

        {/* Barra de Progresso */}
        <div
          className="relative mt-4 cursor-pointer"
          onClick={() => setIsContentVisible(!isContentVisible)}
        >
          <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded-full overflow-hidden">
            <div
              className={`h-full transition-all duration-500 ${
                progressPercentage < 50
                  ? "bg-red-500"
                  : progressPercentage < 80
                  ? "bg-yellow-500"
                  : "bg-green-500"
              }`}
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
          <div className="absolute inset-0 flex justify-center items-center">
            <span className="text-sm font-semibold text-gray-700 dark:text-gray-200">
              {progressPercentage}%
            </span>
          </div>
        </div>

        {/* Detalhamento do Progresso */}
        {isContentVisible && (
          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-900 rounded-lg shadow-lg">
            <h4 className="font-semibold mb-3 text-lg text-gray-800 dark:text-gray-200">
              Detalhamento do Progresso
            </h4>
            <ul className="space-y-2 max-h-60 overflow-y-auto">
              {progressDetails.map((item, index) => (
                <li
                  key={index}
                  className={`flex items-center gap-3 p-2 rounded-md ${
                    item.isAnswered
                      ? "bg-green-100 dark:bg-green-900"
                      : "bg-red-100 dark:bg-red-900"
                  }`}
                >
                  <span
                    className={`flex items-center text-xl ${
                      item.isAnswered ? "text-green-600" : "text-red-600"
                    }`}
                  >
                    {item.isAnswered ? <FaCheck /> : <FaTimes />}
                  </span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {item.title}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Ações */}
        <div className="flex items-center justify-between mt-4">
          {/* Container para os botões principais */}
          <div className="flex items-center gap-2 w-full">
            {/* Versão para telas maiores */}
            <div className="hidden sm:flex items-center justify-between w-full">
              {/* Container dos botões da esquerda */}
              <div className="flex items-center gap-2">
                {/* Botão ou Tag de Recuperação */}
                <div className="w-[120px]">
                  {inRecovery ? (
                    <div className="h-[38px] border border-green-500 bg-white text-green-500 text-sm px-3 rounded-md flex items-center justify-center whitespace-nowrap">
                      <FaTag className="mr-1" />
                      Recuperação
                    </div>
                  ) : (
                    !opportunity.completed && page !== "vendedores" && (
                      <RecoveryButton 
                        crmId={crmId} 
                        onRecoverySuccess={handleRecoverySuccess}
                        isDisabled={hasInvalidContact || isGnBradda}
                        reason={hasInvalidContact ? 'invalid-contact' : isGnBradda ? 'gn-bradda' : undefined}
                      />
                    )
                  )}
                </div>

                {/* Botão Exibir */}
                <button
                  onClick={() => {
                    setSelectedFormData(parsedFormData);
                    openFormDataModal();
                  }}
                  className="w-[120px] h-[38px] px-4 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors whitespace-nowrap flex items-center justify-center"
                >
                  <FaEye className="mr-1" />
                  Exibir
                </button>

                {/* Botão Excluir */}
                {!opportunity.completed && (
                  <button
                    className="w-[120px] h-[38px] flex items-center justify-center px-4 bg-red-500 text-white text-sm rounded-md hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-400 whitespace-nowrap"
                    onClick={() => setIsDeleteModalOpen(true)}
                  >
                    <FaTimesCircle className="mr-1" />
                    Excluir
                  </button>
                )}
              </div>
            </div>

            {/* Versão para telas menores */}
            <div className="flex sm:hidden items-center gap-2 w-full">
              {/* Botão Exibir - Sempre visível */}
              <button
                onClick={() => {
                  setSelectedFormData(parsedFormData);
                  openFormDataModal();
                }}
                className="flex-1 h-[38px] px-4 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors whitespace-nowrap flex items-center justify-center"
              >
                <FaEye className="mr-1" />
                Exibir
              </button>

              {/* Menu Dropdown */}
              {(!inRecovery || !opportunity.completed) && (
                <div className="relative" ref={menuRef}>
                  <button
                    onClick={() => setShowMenu(!showMenu)}
                    className="h-[38px] w-[38px] flex items-center justify-center bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400"
                  >
                    <FaEllipsisV />
                  </button>

                  {/* Menu Dropdown Content */}
                  {showMenu && (
                    <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-50 py-1 border border-gray-200 dark:border-gray-700">
                      {!inRecovery && !opportunity.completed && page !== "vendedores" && (
                        <RecoveryButton 
                          crmId={crmId} 
                          isMenuItem 
                          onRecoverySuccess={handleRecoverySuccess}
                          isDisabled={hasInvalidContact || isGnBradda}
                          reason={hasInvalidContact ? 'invalid-contact' : isGnBradda ? 'gn-bradda' : undefined}
                        />
                      )}
                      {!opportunity.completed && (
                        <button
                          onClick={() => {
                            setIsDeleteModalOpen(true);
                            setShowMenu(false);
                          }}
                          className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900 flex items-center"
                        >
                          <FaTimesCircle className="mr-2" />
                          Excluir
                        </button>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <ConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteOpportunity}
        opportunityName={opportunity.formData?.brandTitle || "Oportunidade"}
      />
    </>
  );
};

export default OpportunityCard;
