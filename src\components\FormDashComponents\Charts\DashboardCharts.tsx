// frontend/components/FormDashComponents/Charts/DashboardCharts.tsx
/* eslint-disable */
import { InitialChartData, StackedBarChartData } from "@/types/crm";
import React from "react";
import InitialChart from "./ChartComponent";
import StackedBarChart from "./StackedChartStages";

interface DashboardChartsProps {
  initialChartData: InitialChartData;
  stackedBarChartData: StackedBarChartData;
}

const DashboardCharts: React.FC<any> = ({
  initialChartData,
  stackedBarChartData,
}) => {
  const initialChartConfig = {
    labels: initialChartData.labels,
    datasets: [
      {
        label: "Formulários Incompletos",
        data: initialChartData.incompleteCounts,
        backgroundColor: "rgba(255, 159, 64, 0.2)",
      },
      {
        label: "Formulários Completos",
        data: initialChartData.completeCounts,
        backgroundColor: "rgba(75, 192, 192, 0.2)",
      },
    ],
  };

  return (
    <div className="p-6 flex flex-col gap-4 h-full bg-gray-100 text-gray-900 dark:bg-gray-900 dark:text-gray-100">
      {/* Primeiro gráfico */}
      <div className="bg-white border flex-1 border-gray-300 p-6 rounded-lg hover:shadow-xl transition-shadow duration-300 dark:bg-gray-200 dark:border-gray-700">
        <InitialChart data={initialChartConfig} />
      </div>
      {/* Segundo gráfico */}
      <div className="bg-white border flex-1 border-gray-300 p-6 rounded-lg hover:shadow-xl transition-shadow duration-300 dark:bg-gray-200 dark:border-gray-700">
        <StackedBarChart stackedBarChartData={stackedBarChartData} />
      </div>
    </div>
  );
};

export default DashboardCharts;
