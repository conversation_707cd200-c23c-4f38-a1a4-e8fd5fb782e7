// utils/store/useLeadStore.ts
import { create } from "zustand";

interface LeadStore {
  modalTitle: string;
  setModalTitle: (title: string) => void;

  leadRecoveryStatus: Record<string, boolean>;
  setLeadRecoveryStatus: (crmId: string, inRecovery: boolean) => void;

  leadsInRecovery: Record<string, boolean>;
  setLeadInRecovery: (crmId: string, inRecovery: boolean) => void;

  leadSalesStatus: Record<string, boolean>;
  setLeadSalesStatus: (crmId: string, inSales: boolean) => void;
}

const useLeadStore = create<LeadStore>((set) => ({
  modalTitle: "",
  setModalTitle: (title: string) => set({ modalTitle: title }),

  leadRecoveryStatus: {},
  setLeadRecoveryStatus: (crmId: string, inRecovery: boolean) =>
    set((state) => ({
      leadRecoveryStatus: {
        ...state.leadRecoveryStatus,
        [crmId]: inRecovery,
      },
    })),

  leadsInRecovery: {},
  setLeadInRecovery: (crmId: string, inRecovery: boolean) =>
    set((state) => ({
      leadsInRecovery: {
        ...state.leadsInRecovery,
        [crmId]: inRecovery,
      },
    })),

  leadSalesStatus: {},
  setLeadSalesStatus: (crmId: string, inSales: boolean) =>
    set((state) => ({
      leadSalesStatus: {
        ...state.leadSalesStatus,
        [crmId]: inSales,
      },
    })),
}));

export default useLeadStore;
