import React, { useMemo} from "react";
import {
  format,
  addMonths,
  subMonths,
  startOfMonth,
  endOfMonth,
  startOfWeek,
  endOfWeek,
  addDays,
  isSameDay,
  isSameMonth,
} from "date-fns";
import { ptBR } from "date-fns/locale";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";
import { useCalendarStore } from "@/context/useReactStatesStore";


const FullCalendar: React.FC = () => {
  const {
    currentMonth,
    setCurrentMonth,
    selectedDate,
    setSelectedDate,
    openListModal,
    formsGroupedByDate,
  } = useCalendarStore();

  // Navegar entre meses
  const handlePrevMonth = () => setCurrentMonth(subMonths(currentMonth, 1));
  const handleNextMonth = () => setCurrentMonth(addMonths(currentMonth, 1));

  // Gerar as células do calendário
  const generateCalendarCells = () => {
    const startDate = startOfWeek(startOfMonth(currentMonth));
    const endDate = endOfWeek(endOfMonth(currentMonth));
    const days = [];
    let day = startDate;

    while (day <= endDate) {
      days.push(day);
      day = addDays(day, 1);
    }
    return days;
  };

  const calendarCells = useMemo(generateCalendarCells, [currentMonth]);
  const daysOfWeek = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];

  return (
    <div className="bg-gray-50 dark:bg-gray-800 rounded-xl shadow-lg p-6 transition-all duration-300 ease-in-out">
      {/* Cabeçalho do Calendário */}
      <div className="flex items-center justify-between mb-6">
        <button
          className="p-2 rounded-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 transition-all"
          onClick={handlePrevMonth}
          aria-label="Mês Anterior"
        >
          <FaChevronLeft className="text-gray-600 dark:text-gray-300" />
        </button>
        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
          {format(currentMonth, "MMMM yyyy", { locale: ptBR })}
        </h2>
        <button
          className="p-2 rounded-full bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 transition-all"
          onClick={handleNextMonth}
          aria-label="Próximo Mês"
        >
          <FaChevronRight className="text-gray-600 dark:text-gray-300" />
        </button>
      </div>

      {/* Dias da Semana */}
      <div className="grid grid-cols-7 gap-2 mb-4 text-gray-700 dark:text-gray-300">
        {daysOfWeek.map((day, index) => (
          <div key={index} className="text-center font-medium">
            {day}
          </div>
        ))}
      </div>

      {/* Células do Calendário */}
      <div className="grid grid-cols-7 gap-3">
        {calendarCells.map((day, index) => {
          const dateKey = format(day, "yyyy-MM-dd");
          const formsForDay = formsGroupedByDate[dateKey] || [];
          const formCount = formsForDay.length;
          const isToday = isSameDay(day, new Date());
          const isSelected = selectedDate && isSameDay(day, selectedDate);
          const isCurrentMonth = isSameMonth(day, currentMonth);
       
          return (
            <button
              key={index}
              onClick={() => {
                setSelectedDate(day);
                openListModal();
              }}
              className={`relative h-14 rounded-lg transition-all duration-200 ${
                isSelected
                  ? "bg-gray-100 text-gray-900 dark:bg-gray-600 dark:text-white border-2 border-gray-300 dark:border-gray-500"
                  : isToday
                  ? "bg-gray-50 text-gray-900 dark:bg-gray-700 dark:text-white border border-gray-200 dark:border-gray-600"
                  : "bg-white dark:bg-gray-700"
              } ${
                isCurrentMonth
                  ? "text-gray-900 dark:text-gray-200"
                  : "text-gray-400 dark:text-gray-500"
              } hover:shadow-md hover:bg-gray-50 dark:hover:bg-gray-600`}
            >
              {/* Número do dia - Centralizado */}
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-lg font-medium">{format(day, "d")}</span>
              </div>

              {/* Indicador de formulários - Círculo vermelho pequeno */}
              {formCount > 0 && (
                <div className="absolute bottom-1 right-1">
                  <div className="min-w-[18px] h-[18px] flex items-center justify-center rounded-full bg-red-500 dark:bg-red-600">
                    <span className="text-[10px] font-medium text-white">
                      {formCount}
                    </span>
                  </div>
                </div>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default FullCalendar;
