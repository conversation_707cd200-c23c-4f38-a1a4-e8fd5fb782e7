import { create } from 'zustand';
import { MarketingFilters, MarketingLead, MarketingLeadsResponse } from '@/types/marketing';

interface MarketingStore {
  leads: MarketingLead[];
  allLeads: MarketingLead[];
  isLoading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  filters: MarketingFilters;
  showTestLeads: boolean;
  setShowTestLeads: (show: boolean) => void;
  setFilters: (filters: Partial<MarketingFilters>) => void;
  fetchLeads: (token: string) => Promise<void>;
  exportToCSV: () => void;
}

const ITEMS_PER_PAGE = 1200;

// Função auxiliar para converter data UTC para o timezone do Brasil (UTC-3)
const convertToBrazilianTime = (utcString: string) => {
  const date = new Date(utcString);
  return new Date(date.getTime() - (3 * 60 * 60 * 1000)); // UTC-3
};

// Função auxiliar para obter o início do dia em UTC
const getStartOfDayUTC = (dateString: string) => {
  const date = new Date(dateString);
  const utcDate = new Date(Date.UTC(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    0, 0, 0, 0
  ));
  return utcDate;
};

// Função auxiliar para obter o fim do dia em UTC
const getEndOfDayUTC = (dateString: string) => {
  const date = new Date(dateString);
  const utcDate = new Date(Date.UTC(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    23, 59, 59, 999
  ));
  return utcDate;
};

const useMarketingStore = create<MarketingStore>((set, get) => ({
  leads: [],
  allLeads: [],
  isLoading: false,
  error: null,
  currentPage: 1,
  totalPages: 1,
  showTestLeads: false,
  filters: {
    page: 1,
  },

  setShowTestLeads: (show) => {
    set((state) => {
      const updatedState = { ...state, showTestLeads: show };
      const filteredLeads = filterLeads(state.allLeads, state.filters, show);
      const totalPages = Math.ceil(filteredLeads.length / ITEMS_PER_PAGE);
      
      return {
        ...updatedState,
        totalPages,
        leads: filteredLeads.slice((state.currentPage - 1) * ITEMS_PER_PAGE, state.currentPage * ITEMS_PER_PAGE),
      };
    });
  },

  setFilters: (newFilters) => {
    set((state) => {
      const updatedFilters = { ...state.filters, ...newFilters };
      const filteredLeads = filterLeads(state.allLeads, updatedFilters, state.showTestLeads);
      const totalPages = Math.ceil(filteredLeads.length / ITEMS_PER_PAGE);
      const page = newFilters.page || 1;
      
      return {
        filters: updatedFilters,
        currentPage: page,
        totalPages,
        leads: filteredLeads.slice((page - 1) * ITEMS_PER_PAGE, page * ITEMS_PER_PAGE),
      };
    });
  },

  fetchLeads: async (token) => {
    set({ isLoading: true, error: null });
    try {
      const response = await fetch('/api/marketing/leads', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Falha ao carregar os leads');
      }

      const data: MarketingLeadsResponse = await response.json();
      const allLeads = data.data || [];
      const filteredLeads = filterLeads(allLeads, get().filters, get().showTestLeads);
      const totalPages = Math.ceil(filteredLeads.length / ITEMS_PER_PAGE);

      set({
        allLeads,
        leads: filteredLeads.slice(0, ITEMS_PER_PAGE),
        totalPages,
        currentPage: 1,
        isLoading: false,
      });
    } catch (error) {
      set({ error: (error as Error).message, isLoading: false });
    }
  },

  exportToCSV: () => {
    const { allLeads, filters } = get();
    const filteredLeads = filterLeads(allLeads, filters, get().showTestLeads);
    
    const headers = [
      'ID',
      'Data',
      'IP',
      'Campanha',
      'UTM Source',
      'UTM Medium',
      'UTM Campaign',
      'UTM Content',
      'UTM Term',
      'Nome do Lead',
      'Email do Lead',
      'Telefone do Lead',
      'Score'
    ];

    const csvContent = [
      headers.join(','),
      ...filteredLeads.map(lead => [
        lead.id,
        new Date(lead.timestamp).toLocaleString('pt-BR'),
        lead.ipAddress,
        lead.campaignId,
        lead.utmSource || '',
        lead.utmMedium || '',
        lead.utmCampaign || '',
        lead.utmContent || '',
        lead.utmTerm || '',
        lead.Opportunity?.leadName || '',
        lead.Opportunity?.leadMail || '',
        lead.Opportunity?.leadNumber || '',
        lead.Opportunity?.score || '',
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `leads_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  },
}));

const filterLeads = (leads: MarketingLead[], filters: MarketingFilters, showTestLeads: boolean): MarketingLead[] => {
  if (!Array.isArray(leads)) return [];
  
  return leads.filter(lead => {
    // Filtro de leads de teste
    if (!showTestLeads && lead.campaignId?.toUpperCase().includes('TESTE')) {
      return false;
    }

    // Converte o timestamp UTC do lead para horário do Brasil
    const leadDate = convertToBrazilianTime(lead.timestamp);
    
    // Filtro de data início
    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      startDate.setHours(0, 0, 0, 0);
      if (leadDate < startDate) {
        return false;
      }
    }

    // Filtro de data fim
    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      endDate.setHours(23, 59, 59, 999);
      if (leadDate > endDate) {
        return false;
      }
    }

    // Filtro de texto (busca em vários campos)
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase();
      const searchFields = [
        lead.campaignId,
        lead.utmSource,
        lead.utmMedium,
        lead.utmCampaign,
      ].map(field => field?.toLowerCase() || '');

      return searchFields.some(field => field.includes(searchTerm));
    }

    return true;
  });
};

export default useMarketingStore; 