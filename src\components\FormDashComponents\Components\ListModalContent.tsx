import React, { useState } from "react";
import OpportunityCard from "@/components/FormDashComponents/OpportunityCard";
import { Opportunity } from "@/types/crm";
import FilterComponent from "./FilterComponent";
import { FaExclamationCircle, FaSort, FaChevronLeft, FaChevronRight } from "react-icons/fa";
import { format, addDays, subDays, addMonths, subMonths, isToday, isAfter } from "date-fns";
import { ptBR } from "date-fns/locale";
import { DateRange } from "react-day-picker";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface ListModalContentProps {
  _totalForms: number;
  filteredForms: Opportunity[];
  scoreFilter: { min: number | ""; max: number | "" };
  setScoreFilter: React.Dispatch<React.SetStateAction<{ min: number | ""; max: number | "" }>>;
  formProgressFilter: string;
  setFormProgressFilter: React.Dispatch<React.SetStateAction<string>>;
  uniqueFormProgressValues: string[];
  uniqueBrandTitles: string[];
  brandTitleFilter: string;
  setBrandTitleFilter: React.Dispatch<React.SetStateAction<string>>;
  onUpdate: () => void;
  _isOpenFormDataModal: boolean;
  selectedDate: Date;
  onDateChange: (date: Date) => void;
  dateRange: DateRange | undefined;
  onDateRangeChange: (range: DateRange | undefined) => void;
}

const ListModalContent: React.FC<ListModalContentProps> = ({
  _totalForms,
  filteredForms,
  scoreFilter,
  setScoreFilter,
  formProgressFilter,
  setFormProgressFilter,
  uniqueFormProgressValues,
  uniqueBrandTitles,
  brandTitleFilter,
  setBrandTitleFilter,
  onUpdate,
  _isOpenFormDataModal = false,
  selectedDate,
  onDateChange,
  dateRange,
  onDateRangeChange,
}) => {
  const [isDescending, setIsDescending] = useState(true);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [activeCalendar, setActiveCalendar] = useState<'start' | 'end'>('start');
  const [currentMonth, setCurrentMonth] = useState<Date>(selectedDate);

  const toggleSortOrder = () => {
    setIsDescending(!isDescending);
  };

  const handlePreviousDay = () => {
    onDateChange(subDays(selectedDate, 1));
  };

  const handleNextDay = () => {
    onDateChange(addDays(selectedDate, 1));
  };

  const handleCalendarOpen = () => {
    setIsCalendarOpen(true);
    setActiveCalendar('start');
  };

  const handleCalendarClose = () => {
    setIsCalendarOpen(false);
    setActiveCalendar('start');
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (!date) return;

    if (activeCalendar === 'start') {
      onDateRangeChange({ from: date, to: undefined });
      setActiveCalendar('end');
    } else {
      if (dateRange?.from && isAfter(dateRange.from, date)) {
        // Se a data final for anterior à inicial, inverte as datas
        onDateRangeChange({ from: date, to: dateRange.from });
      } else if (dateRange?.from) {
        onDateRangeChange({ from: dateRange.from, to: date });
      }
      setIsCalendarOpen(false);
    }
  };

  const handlePreviousMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  const formatDateRange = () => {
    if (!dateRange?.from) {
      return (
        <span className="text-gray-900 dark:text-gray-100">
          {format(selectedDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}
        </span>
      );
    }

    if (dateRange.to) {
      return (
        <div className="flex items-center gap-2">
          <div>
            <div className="text-xs text-gray-600 dark:text-gray-400 font-medium">Data inicial</div>
            <div className="text-gray-900 dark:text-gray-100 font-medium">{format(dateRange.from, "dd/MM/yyyy")}</div>
          </div>
          <div className="text-gray-500 dark:text-gray-400">→</div>
          <div>
            <div className="text-xs text-gray-600 dark:text-gray-400 font-medium">Data final</div>
            <div className="text-gray-900 dark:text-gray-100 font-medium">{format(dateRange.to, "dd/MM/yyyy")}</div>
          </div>
        </div>
      );
    }

    return (
      <div className="flex items-center gap-2">
        <div>
          <div className="text-xs text-gray-600 dark:text-gray-400 font-medium">Data inicial</div>
          <div className="text-gray-900 dark:text-gray-100 font-medium">{format(dateRange.from, "dd/MM/yyyy")}</div>
        </div>
        <div className="text-gray-500 dark:text-gray-400">→</div>
        <div>
          <div className="text-xs text-gray-600 dark:text-gray-400 font-medium">Data final</div>
          <div>Selecione</div>
        </div>
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col overflow-hidden">
      {/* Container rolável para todo o conteúdo */}
      <div className="flex-1 overflow-y-auto">
        {/* Cabeçalho com Filtros - Agora dentro da área rolável */}
        <div className="p-4 space-y-4 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-800">
          {/* Navegação de Data */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2 h-8">
              <Button
                variant="outline"
                size="icon"
                onClick={handlePreviousDay}
                className="h-8 w-8 border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                <FaChevronLeft className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              </Button>
              
              <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
                <PopoverTrigger asChild>
                  <Button 
                    variant="outline" 
                    className="flex items-center gap-2 px-4 h-8 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800"
                    onClick={handleCalendarOpen}
                  >
                    <span className="text-gray-900 dark:text-gray-100 font-medium">
                      {formatDateRange()}
                    </span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <div className="p-3 border-b border-neutral-200 dark:border-neutral-800">
                    <div className="flex items-center justify-center gap-4 mb-2">
                      <button 
                        className="flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full h-8 w-8 transition-colors"
                        onClick={handlePreviousMonth}
                      >
                        <FaChevronLeft className="h-4 w-4 text-gray-700 dark:text-gray-300" />
                      </button>
                      <span className="text-base font-medium text-gray-900 dark:text-gray-100">
                        {format(currentMonth, "MMMM'/'yyyy", { locale: ptBR })}
                      </span>
                      <button 
                        className="flex items-center justify-center hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full h-8 w-8 transition-colors"
                        onClick={handleNextMonth}
                      >
                        <FaChevronRight className="h-4 w-4 text-gray-700 dark:text-gray-300" />
                      </button>
                    </div>
                    <p className="text-sm font-medium text-center text-gray-700 dark:text-gray-300">
                      {activeCalendar === 'start' ? (
                        <>
                          <span className="block text-lg font-semibold text-gray-900 dark:text-gray-100">Data inicial</span>
                          <span className="text-xs text-gray-600 dark:text-gray-400">Selecione a data de início do período</span>
                        </>
                      ) : (
                        <>
                          <span className="block text-lg font-semibold text-gray-900 dark:text-gray-100">Data final</span>
                          <span className="text-xs text-gray-600 dark:text-gray-400">Selecione a data de fim do período</span>
                        </>
                      )}
                    </p>
                  </div>
                  <Calendar
                    mode="single"
                    selected={activeCalendar === 'start' ? dateRange?.from : dateRange?.to}
                    onSelect={handleDateSelect}
                    month={currentMonth}
                    defaultMonth={currentMonth}
                    fromDate={activeCalendar === 'end' && dateRange?.from ? dateRange.from : undefined}
                    locale={ptBR}
                    showOutsideDays={false}
                    fixedWeeks
                    formatters={{
                      formatCaption: () => ""
                    }}
                    modifiers={{
                      today: (date) => isToday(date)
                    }}
                    modifiersClassNames={{
                      today: "text-indigo-600 dark:text-indigo-400 font-semibold"
                    }}
                    classNames={{
                      nav: "hidden",
                      nav_button: "hidden",
                      nav_button_previous: "hidden",
                      nav_button_next: "hidden",
                      caption: "hidden",
                      caption_label: "hidden",
                      head_cell: "text-xs font-medium text-gray-600 dark:text-gray-400 w-10 h-10",
                      table: "w-full border-collapse space-y-1",
                      cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-gray-100 dark:[&:has([aria-selected])]:bg-gray-700 first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
                      day: "h-9 w-9 p-0 font-normal text-sm text-gray-900 dark:text-gray-100 aria-selected:opacity-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors duration-200 text-center",
                      day_selected: "bg-indigo-600 text-white hover:bg-indigo-600 hover:text-white focus:bg-indigo-600 focus:text-white dark:bg-indigo-500 dark:hover:bg-indigo-500 rounded-full",
                      day_today: "text-indigo-600 dark:text-indigo-400 font-semibold",
                      day_outside: "text-gray-400 dark:text-gray-600 opacity-50",
                      day_disabled: "text-gray-400 dark:text-gray-600 opacity-50",
                      day_range_middle: "aria-selected:bg-gray-100 dark:aria-selected:bg-gray-700 rounded-none",
                      day_hidden: "invisible"
                    }}
                  />
                  <div className="p-3 border-t border-neutral-200 dark:border-neutral-800">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={handleCalendarClose}
                    >
                      Cancelar
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>

              <Button
                variant="outline"
                size="icon"
                onClick={handleNextDay}
                className="h-8 w-8 border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800"
              >
                <FaChevronRight className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              </Button>
            </div>

            <div className="text-sm text-gray-700 dark:text-gray-300">
              Filtrados: {filteredForms.length}
            </div>
          </div>

          {/* Filtros */}
          <FilterComponent
            scoreFilter={scoreFilter}
            setScoreFilter={setScoreFilter}
            _formProgressFilter={formProgressFilter}
            setFormProgressFilter={setFormProgressFilter}
            _uniqueFormProgressValues={uniqueFormProgressValues}
            _uniqueBrandTitles={uniqueBrandTitles}
            _brandTitleFilter={brandTitleFilter}
            setBrandTitleFilter={setBrandTitleFilter}
          />

          {/* Botão de Sort */}
          <div className="flex justify-between items-center mt-4">
            <button
              className="flex items-center gap-2 text-sm text-gray-700 hover:text-gray-900 font-medium dark:text-gray-300 dark:hover:text-gray-100"
              onClick={toggleSortOrder}
            >
              <FaSort />
              {isDescending ? "Mais recentes" : "Mais antigos"}
            </button>
          </div>
        </div>

        {/* Lista de OpportunityCards */}
        <div className="p-4">
          {filteredForms.length > 0 ? (
            <div className="flex flex-wrap gap-6 justify-center content-start min-h-min pb-8">
              {filteredForms
                .sort((a, b) => {
                  const getLastTimestamp = (opportunity: Opportunity) =>
                    opportunity.Log && opportunity.Log.length > 0
                      ? new Date(
                          opportunity.Log[opportunity.Log.length - 1].timestamp
                        ).getTime()
                      : 0;

                  return isDescending
                    ? getLastTimestamp(b) - getLastTimestamp(a)
                    : getLastTimestamp(a) - getLastTimestamp(b);
                })
                .map((opp) => (
                  <OpportunityCard
                    key={opp.crmId}
                    opportunity={opp}
                    onUpdate={onUpdate}
                  />
                ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center mt-6">
              <FaExclamationCircle className="text-4xl text-gray-500 dark:text-gray-400 mb-4" />
              <p className="text-center text-gray-700 dark:text-gray-300">
                Nenhum formulário encontrado com os filtros aplicados.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default ListModalContent;
