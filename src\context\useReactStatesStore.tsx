import { create } from "zustand";
import { Opportunity } from "@/types/crm";

interface CalendarState {
  // Estados existentes
  selectedDate: Date | null;
  setSelectedDate: (date: Date | null) => void;
  isListModalOpen: boolean;
  toggleListModal: () => void;
  openListModal: () => void; // Para abrir o modal
  closeListModal: () => void; // Para fechar o modal
  formsGroupedByDate: Record<string, Opportunity[]>;
  setFormsGroupedByDate: (forms: Record<string, Opportunity[]>) => void;
  isCalendarMinimized: boolean;
  setIsCalendarMinimized: (isMinimized: boolean) => void;

  // Novos estados
  currentMonth: Date;
  setCurrentMonth: (date: Date) => void;
  inProgressForms: Opportunity[];
  setInProgressForms: (forms: Opportunity[]) => void;
}

// Criar a store Zustand
export const useCalendarStore = create<CalendarState>((set) => ({
  // Estados existentes
  selectedDate: null,
  setSelectedDate: (date) => set({ selectedDate: date }),
  isListModalOpen: false,
  openListModal: () => set({ isListModalOpen: true }),
  closeListModal: () => set({ isListModalOpen: false }),
  toggleListModal: () =>
    set((state) => ({ isListModalOpen: !state.isListModalOpen })),
  formsGroupedByDate: {},
  setFormsGroupedByDate: (forms) => set({ formsGroupedByDate: forms }),
  isCalendarMinimized: false,
  setIsCalendarMinimized: (isMinimized) => set({ isCalendarMinimized: isMinimized }),

  // Novos estados
  currentMonth: new Date(),
  setCurrentMonth: (date) => set({ currentMonth: date }),
  inProgressForms: [],
  setInProgressForms: (forms) => set({ inProgressForms: forms }),
}));
