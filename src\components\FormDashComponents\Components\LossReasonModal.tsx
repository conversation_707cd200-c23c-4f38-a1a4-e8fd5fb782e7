"use client";

import React, { useState, useRef } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON><PERSON>eader,
  DialogFooter,
  DialogTitle,
} from "../../ui/dialog";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "../../ui/select";
import { Button } from "../../ui/button";
import { Input } from "../../ui/input"; // Para o campo de comentário

interface LossReason {
  id: number;
  name: string;
}

interface LossReasonModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (comment: string, lostReasonId: number) => void;
  reasons: LossReason[];
}

const LossReasonModal: React.FC<LossReasonModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  reasons,
}) => {
  const [selectedReasonId, setSelectedReasonId] = useState<number>(226679); // <PERSON><PERSON><PERSON>
  const [comment, setComment] = useState<string>("");

  const dialogRef = useRef<HTMLDivElement>(null);

  const handleConfirm = () => {
    onConfirm(comment, selectedReasonId);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent ref={dialogRef} onClick={(e) => e.stopPropagation()}>
        <DialogHeader>
          <DialogTitle>
            Escolha o motivo da perda e adicione um comentário
          </DialogTitle>
        </DialogHeader>
        <Select onValueChange={(value) => setSelectedReasonId(Number(value))}>
          <SelectTrigger className="w-full mt-4">
            <SelectValue placeholder="Selecione um motivo" />
          </SelectTrigger>
          <SelectContent>
            {reasons.map((reason) => (
              <SelectItem key={reason.id} value={String(reason.id)}>
                {reason.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Input
          className="mt-4"
          placeholder="Adicionar comentário (opcional)"
          value={comment}
          onChange={(e: { target: { value: React.SetStateAction<string>; }; }) => setComment(e.target.value)}
        />
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button onClick={handleConfirm}>Confirmar</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default LossReasonModal;
