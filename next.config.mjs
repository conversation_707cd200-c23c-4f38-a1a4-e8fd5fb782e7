/** @type {import('next').NextConfig} */
const nextConfig = {
  productionBrowserSourceMaps: false, // Desativa source maps para produção

  // Desabilita o ESLint durante o build
  eslint: {
    ignoreDuringBuilds: true,
  },

  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'assets.pipe.run',
        pathname: '**',
      },
    ],
  },

  webpack: (config) => {
    // Adiciona um loader para evitar que pacotes de terceiros carreguem mapas ausentes
    config.module.rules.push({
      test: /\.js$/,
      enforce: "pre",
      use: ["source-map-loader"],
      exclude: /node_modules/, // Evita que dependências externas tentem carregar source maps
    });

    return config;
  },

  // Mantém o strict mode
  reactStrictMode: true,
  
  // Desativa source maps em produção para arquivos CSS
  optimizeFonts: true,
  sassOptions: {
    sourceMap: false,
  },
  
  // Configuração para lidar com source maps externos
  experimental: {
    optimizePackageImports: ['react-toastify'],
  },
};

export default nextConfig;
