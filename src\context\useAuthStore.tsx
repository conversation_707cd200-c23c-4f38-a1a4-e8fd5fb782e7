import {create} from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import axios from "axios";

interface AuthState {
  token: string | null;
  user: { email: string } | null;
  showLoginModal: boolean;
  login: (token: string) => void;
  logout: () => void;
  setShowLoginModal: (show: boolean) => void;
  verifyToken: () => Promise<boolean>;
}

const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      token: null, // Define o token como null inicialmente
      user: null, // Define o usuário como null inicialmente
      showLoginModal: true,
      login: (token: string) => {
        const payload = JSON.parse(atob(token.split(".")[1]));
        set({ token, user: { email: payload.email }, showLoginModal: false });
      },
      logout: async () => {
        try {
          await axios.post("http://localhost:3060/auth/logout");
        } catch (error) {
          console.error("Erro ao fazer logout:", error);
        }
        set({ token: null, user: null, showLoginModal: true });
      },
      setShowLoginModal: (show: boolean) => set({ showLoginModal: show }),
      verifyToken: async () => {
        const token = useAuthStore.getState().token;
        if (!token) return false;
        try {
          const response = await axios.get(
            "http://localhost:3060/auth/profile",
            {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            }
          );
          const { email } = response.data;
          set({ user: { email }, showLoginModal: false });
          return true;
        } catch (error) {
          console.error("Token inválido ou expirado:", error);
          set({ token: null, user: null, showLoginModal: true });
          return false;
        }
      },
    }),
    {
      name: "auth-storage", // Nome da chave no localStorage
      storage: createJSONStorage(() => localStorage), // Utiliza createJSONStorage para persistência
      onRehydrateStorage: (state) => {
        if (state?.token) {
          const payload = JSON.parse(atob(state.token.split(".")[1]));
          state.user = { email: payload.email }; // Garante que o user seja restaurado adequadamente
        }
      },
    }
  )
);

export default useAuthStore;
