// context/useDarkModeStore.ts
import {create} from "zustand";

interface DarkModeState {
  isDarkMode: boolean;
  toggleDarkMode: () => void;
}

const useDarkModeStore = create<DarkModeState>((set) => {
  // Determinar o estado inicial do modo escuro
  let initialDarkMode = false;
  if (typeof window !== "undefined") {
    const storedMode = localStorage.getItem("theme");
    if (storedMode === "dark") {
      initialDarkMode = true;
    } else if (storedMode === "light") {
      initialDarkMode = false;
    } else {
      // Se não houver preferência armazenada, use a preferência do sistema
      initialDarkMode = window.matchMedia(
        "(prefers-color-scheme: dark)"
      ).matches;
    }

    // Aplicar o estado inicial no DOM
    if (initialDarkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }

    // Listener para mudanças na preferência de cor do sistema operacional
    window
      .matchMedia("(prefers-color-scheme: dark)")
      .addEventListener("change", (e) => {
        if (!localStorage.getItem("theme")) {
          set({ isDarkMode: e.matches });
          if (e.matches) {
            document.documentElement.classList.add("dark");
          } else {
            document.documentElement.classList.remove("dark");
          }
        }
      });
  }

  return {
    isDarkMode: initialDarkMode,
    toggleDarkMode: () =>
      set((state) => {
        const newMode = !state.isDarkMode;
        if (typeof window !== "undefined") {
          if (newMode) {
            document.documentElement.classList.add("dark");
            localStorage.setItem("theme", "dark");
          } else {
            document.documentElement.classList.remove("dark");
            localStorage.setItem("theme", "light");
          }
        }
        return { isDarkMode: newMode };
      }),
  };
});

export default useDarkModeStore;
