// components/FormDashComponents/SideBar.tsx
"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  FaMoon,
  FaSun,
  FaSignOutAlt,
  FaHome,
  FaCheckCircle,
  FaHourglassHalf,
  FaClipboardList,
  FaChevronLeft,
  FaChevronRight,
  FaUser,
  FaChartLine,
} from "react-icons/fa";
import useDarkModeStore from "@/context/useDarkModeStore";
import { useFormDataModalStore } from "@/context/useFormDataModalStore";

interface SidebarProps {
  onLogout: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ onLogout }) => {
  const pathname = usePathname();
  const { isDarkMode, toggleDarkMode } = useDarkModeStore();
  const { isFormDataModalOpen } = useFormDataModalStore();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [showNewBadge, setShowNewBadge] = useState(true);

  useEffect(() => {
    if (isFormDataModalOpen) {
      setIsSidebarCollapsed(true);
    }
  }, [isFormDataModalOpen]);

  useEffect(() => {
    // Verifica se já mostrou o badge antes
    const hasSeenNewBadge = localStorage.getItem('hasSeenMarketingBadge');
    if (hasSeenNewBadge) {
      setShowNewBadge(false);
    }

    // Remove o badge após 7 dias
    const now = new Date();
    const releaseDate = new Date('2024-03-14'); // Data de lançamento da feature
    if (now.getTime() - releaseDate.getTime() > 7 * 24 * 60 * 60 * 1000) {
      setShowNewBadge(false);
      localStorage.setItem('hasSeenMarketingBadge', 'true');
    }
  }, []);

  const handleMarketingClick = () => {
    setShowNewBadge(false);
    localStorage.setItem('hasSeenMarketingBadge', 'true');
  };

  const tabs = [
    { name: "Inicial", path: "/", icon: <FaHome className="w-5 h-5" /> },
    {
      name: "Vendedores",
      path: "/vendedores",
      icon: <FaUser className="w-5 h-5" />,
    },
    {
      name: "Formulários em Progresso", 
      path: "/formsEmProgresso",
      icon: <FaHourglassHalf className="w-5 h-5" />,
    },
    { name: "Logs", path: "/logs", icon: <FaClipboardList className="w-5 h-5" /> },
    {
      name: "Marketing",
      path: "/marketing",
      icon: <FaChartLine className="w-5 h-5" />,
      isNew: showNewBadge,
    },
  ];

  const sidebarWidthClass = isSidebarCollapsed ? "w-20" : "w-72";

  return (
    <aside
      className={`${sidebarWidthClass} min-h-screen flex flex-col justify-between bg-white dark:bg-gray-900 shadow-lg transition-all duration-300 ease-in-out`}
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-800">
          <div className="flex flex-col items-center space-y-4">
            <button
              onClick={toggleDarkMode}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              aria-label="Alternar tema"
            >
              {isDarkMode ? (
                <FaSun className="w-5 h-5 text-amber-400" />
              ) : (
                <FaMoon className="w-5 h-5 text-gray-600" />
              )}
            </button>

            <button
              onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              aria-label="Alternar sidebar"
            >
              {isSidebarCollapsed ? (
                <FaChevronRight className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              ) : (
                <FaChevronLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              )}
            </button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-3 py-4">
          <ul className="space-y-2">
            {tabs.map((tab) => (
              <li key={tab.path}>
                <Link
                  href={tab.path}
                  onClick={tab.path === '/marketing' ? handleMarketingClick : undefined}
                  className={`flex items-center ${
                    isSidebarCollapsed ? "justify-center" : "justify-start"
                  } px-3 py-2.5 rounded-lg transition-colors relative ${
                    pathname === tab.path
                      ? "bg-indigo-50 dark:bg-indigo-900/50 text-indigo-600 dark:text-indigo-400"
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                  }`}
                >
                  <span className="flex items-center justify-center w-5 h-5">
                    {tab.icon}
                  </span>
                  {!isSidebarCollapsed && (
                    <span className="ml-3 text-sm font-medium whitespace-nowrap">{tab.name}</span>
                  )}
                  {tab.isNew && (
                    <span className={`absolute ${isSidebarCollapsed ? "top-0 right-0" : "top-1 right-2"} flex h-5 w-10`}>
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-indigo-400 opacity-75"></span>
                      <span className="relative inline-flex rounded-full h-5 px-2 bg-indigo-500 text-white text-xs items-center justify-center">
                        Novo
                      </span>
                    </span>
                  )}
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-800">
          <button
            onClick={onLogout}
            className={`w-full flex items-center ${
              isSidebarCollapsed ? "justify-center" : "justify-start"
            } px-4 py-2.5 rounded-lg bg-red-500 hover:bg-red-600 text-white transition-colors`}
          >
            <span className="flex items-center justify-center w-5 h-5">
              <FaSignOutAlt className="w-5 h-5" />
            </span>
            {!isSidebarCollapsed && (
              <span className="ml-2 text-sm font-medium">Sair</span>
            )}
          </button>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
