// frontend/components/FormDashComponents/Charts/StackedBarChart.tsx

"use client";

import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  TooltipItem,
} from "chart.js";
import React, { useEffect, useMemo, useState, useRef } from "react";
import { LeadCard } from "../Components/LeadCard";
import {
  Opportunity,
  StageDataItem,
  StageHistory,
  StackedBarChartData,
} from "@/types/crm";
import { pipelineStageMapping } from "@/utils/stagesMapping";

// Registro dos componentes do Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

/**
 * Função para verificar se uma oportunidade está em recuperação.
 * @param opp - A oportunidade a ser verificada.
 * @returns Boolean indicando se está em recuperação.
 */
const isInRecovery = (opp: Opportunity): boolean => {
  return opp?.stageId === "506789";
};

/**
 * Função para verificar se uma oportunidade passou pela recuperação e está no funil de vendas.
 * @param opp - A oportunidade a ser verificada.
 * @returns Boolean indicando se passou pela recuperação e está no funil de vendas.
 */
const hasRecoveredAndInSales = (opp: Opportunity): boolean => {
  if (!opp) return false;

  const inSalesFunnel = opp.pipelineId === "56894";
  if (!inSalesFunnel) return false;

  let histories: StageHistory[] = [];

  if (opp.stageHistories) {
    if (typeof opp.stageHistories === "string") {
      try {
        histories = JSON.parse(opp.stageHistories);
      } catch (error) {
        console.error("Erro ao fazer o parse do stageHistories:", error);
        histories = [];
      }
    } else {
      histories = opp.stageHistories; // Já é um objeto
    }
  }

  // Verifica se já passou pela etapa de recuperação (506789)
  return histories.some(
    (history: StageHistory) => String(history.in_stage_id) === "506789"
  );
};

/**
 * Função para obter o intervalo de datas com base no intervalo selecionado.
 * @param range - O intervalo selecionado.
 * @returns Objeto contendo startDate e endDate.
 */
const getDateRange = (range: string): { startDate: Date; endDate: Date } => {
  const today = new Date();
  const start = new Date(today); // Clone de 'today' para evitar mutação
  const end = new Date(today); // Clone de 'today' para definir 'endDate'

  switch (range) {
    case "last30Days":
      start.setDate(today.getDate() - 30);
      end.setDate(today.getDate() + 1); // Até amanhã 00:00
      break;
    case "last15Days":
      start.setDate(today.getDate() - 15);
      end.setDate(today.getDate() + 1);
      break;
    case "last7Days":
      start.setDate(today.getDate() - 7);
      end.setDate(today.getDate() + 1);
      break;
    case "today":
      start.setHours(0, 0, 0, 0); // Início do dia
      end.setDate(today.getDate() + 1); // Até amanhã 00:00
      break;
    case "thisWeek":
      const dayOfWeek = today.getDay(); // 0 (Domingo) a 6 (Sábado)
      const diff = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // Ajuste para segunda-feira
      start.setDate(today.getDate() + diff);
      start.setHours(0, 0, 0, 0); // Início da semana
      end.setDate(start.getDate() + 7); // Até a próxima segunda-feira
      break;
    case "thisMonth":
      start.setDate(1); // Primeiro dia do mês
      start.setHours(0, 0, 0, 0); // Início do primeiro dia
      end.setMonth(start.getMonth() + 1); // Primeiro dia do próximo mês
      end.setDate(1);
      end.setHours(0, 0, 0, 0); // Início do próximo mês
      break;
    case "total":
    default:
      start.setTime(0); // Data inicial padrão (1970-01-01)
      end.setTime(today.getTime() + 86400000); // Até o final do dia atual
      break;
  }
  return { startDate: start, endDate: end };
};

interface StackedBarChartProps {
  stackedBarChartData: StackedBarChartData;
}

const StackedBarChart: React.FC<StackedBarChartProps> = ({
  stackedBarChartData,
}) => {
  console.log("Renderizando StackedBarChart");
  console.log("StackedBarChartData:", stackedBarChartData);
  // Estados para o modal
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedLeads, setSelectedLeads] = useState<Opportunity[]>([]);
  const [modalTitle, setModalTitle] = useState("");
  // Estados para seleção de intervalo de datas
  const [dateRange, setDateRange] = useState<string>("total");
  const [currentLeadType, setCurrentLeadType] = useState<
    "sales" | "nonSales" | "notDistributed" | "recovery"
  >("sales");
  // Referência para o gráfico
  const chartRef = useRef<ChartJS<"bar", number[], unknown> | null>(null);
  // Obtenção do intervalo de datas baseado na seleção
  const { startDate, endDate } = useMemo(
    () => getDateRange(dateRange),
    [dateRange]
  );

  const opportunities = useMemo(() => [
    ...stackedBarChartData.leads.notDistributed,
    ...stackedBarChartData.leads.recovery,
    ...stackedBarChartData.leads.sales,
  ], [
    stackedBarChartData.leads.notDistributed,
    stackedBarChartData.leads.recovery,
    stackedBarChartData.leads.sales,
  ]);

  /**
   * Filtra as oportunidades com base no intervalo de datas selecionado.
   */
  const filteredOpportunities = useMemo(() => {
    if (dateRange === "total") return opportunities;
    return opportunities.filter((opp) => {
      if (!opp.createdAt) return false;
      const createdAt = new Date(opp.createdAt);
      return createdAt >= startDate && createdAt < endDate;
    });
  }, [opportunities, startDate, endDate, dateRange]);

  /**
   * Agrupa e conta as oportunidades por estágio e tipo de lead.
   */
  const stageData = useMemo(() => {
    return filteredOpportunities.reduce((acc, opp) => {
      const startedInStageHash =
        (typeof opp.startedInStage === "string"
          ? JSON.parse(opp.startedInStage)
          : opp.startedInStage
        )?.hash || "";

      // Verificar se o estágio de criação está mapeado
      const stageName = pipelineStageMapping[startedInStageHash];

      if (!stageName) return acc;

      if (!acc[stageName]) {
        acc[stageName] = {
          sales: 0,
          nonSales: 0,
          notDistributed: 0,
          recovery: 0,
          leads: { sales: [], nonSales: [], notDistributed: [], recovery: [] },
          originalStageName: stageName, // Armazenar o nome original do estágio
        };
      }

      const inRecovery = isInRecovery(opp);
      const recoveredAndInSales = hasRecoveredAndInSales(opp);

      if (inRecovery) {
        acc[stageName].recovery++;
        acc[stageName].leads.recovery.push(opp);
      } else if (recoveredAndInSales) {
        acc[stageName].sales++;
        acc[stageName].leads.sales.push(opp);
      } else if (
        typeof opp.startedInStage !== "string" &&
        opp.startedInStage &&
        opp.stageId === String(opp.startedInStage.id)
      ) {
        acc[stageName].notDistributed++;
        acc[stageName].leads.notDistributed.push(opp);
      } else if (opp.stageId === "488514") {
        acc[stageName].nonSales++;
        acc[stageName].leads.nonSales.push(opp);
      } else if (opp.pipelineId === "56894") {
        acc[stageName].sales++;
        acc[stageName].leads.sales.push(opp);
      }

      return acc;
    }, {} as Record<string, StageDataItem>);
  }, [filteredOpportunities]);

  /**
   * Gera as labels do gráfico com base nos estágios.
   */
  const labels = stackedBarChartData.labels

  /**
   * Configura os dados para o gráfico de barras empilhadas.
   */
  const chartData = useMemo(() => {
    const labels = Object.keys(stageData);

    const datasets = [
      {
        label: "Leads no Funil de Vendas",
        data: labels.map((label) => {
          const sales = stageData[label]?.sales || 0;
          return sales;
        }),
        backgroundColor: "#4C9AFF",
      },
      {
        label: "Não Distribuído",
        data: labels.map((label) => {
          const notDistributed = stageData[label]?.notDistributed || 0;
          return notDistributed;
        }),
        backgroundColor: "#FFD700",
      },
      {
        label: "Leads na Etapa Distribuir pra Vendas",
        data: labels.map((label) => {
          const nonSales = stageData[label]?.nonSales || 0;
          return nonSales;
        }),
        backgroundColor: "#FF6961",
      },
      {
        label: "⛑️ Recuperação",
        data: labels.map((label) => {
          const recovery = stageData[label]?.recovery || 0;
          return recovery;
        }),
        backgroundColor: "#4CAF50",
      },
    ];

    return {
      labels,
      datasets,
    };
  }, [stageData]);

  /**
   * Configura as opções para o gráfico de barras empilhadas.
   */
  const options = useMemo(
    () => ({
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: "top" as const,
        },
        title: {
          display: true,
          text: "Leads por Estágio do Pipeline (Empilhado)",
        },
        tooltip: {
          callbacks: {
            title: (tooltipItems: TooltipItem<"bar">[]) => {
              const index = tooltipItems[0].dataIndex;
              return labels[index];
            },
            label: function (tooltipItem: TooltipItem<"bar">) {
              const datasetLabel = tooltipItem.dataset.label || "";
              const currentValue = tooltipItem.raw as number;
              const dataIndex = tooltipItem.dataIndex;

              let total = 0;
              // Calcular o total com base nos dados do chartData
              chartData.datasets.forEach((dataset) => {
                const value = dataset.data[dataIndex];
                if (typeof value === "number") {
                  total += value;
                }
              });

              if (datasetLabel === "Leads no Funil de Vendas") {
                const percentage =
                  total > 0
                    ? ((currentValue / total) * 100).toFixed(2)
                    : "0.00";
                return `${datasetLabel}: ${currentValue} (${percentage}%)`;
              }

              return `${datasetLabel}: ${currentValue}`;
            },
          },
        },
      },
      scales: {
        x: {
          stacked: true,
        },
        y: {
          stacked: true,
          beginAtZero: true,
          ticks: {
            precision: 0,
          },
        },
      },
    }),
    [labels, chartData.datasets]
  );

  /**
   * Manipulador de clique no gráfico para abrir o modal com detalhes dos leads.
   * @param event - O evento de clique.
   */
  const handleChartClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const chart = chartRef.current;
    if (!chart) return;

    const elements = chart.getElementsAtEventForMode(
      event.nativeEvent,
      "nearest",
      { intersect: true },
      false
    );

    console.log("Chart clicked", elements);

    if (elements.length > 0) {
      const element = elements[0];
      const datasetIndex = element.datasetIndex;
      const index = element.index;

      console.log("Element clicked", element);

      // Garantir que o índice existe e o label está correto
      const stageLabel = labels[index];
      console.log("Stage label:", stageLabel);
      if (!stageLabel) return;

      const datasetLabel = chartData.datasets[datasetIndex]?.label;
      console.log("Dataset label:", datasetLabel);
      if (!datasetLabel) return;

      let leadType: "sales" | "nonSales" | "notDistributed" | "recovery" =
        "sales";
      if (datasetLabel === "Leads no Funil de Vendas") {
        leadType = "sales";
      } else if (datasetLabel === "Leads na Etapa Distribuir pra Vendas") {
        leadType = "nonSales";
      } else if (datasetLabel === "Não Distribuído") {
        leadType = "notDistributed";
      } else if (datasetLabel === "⛑️ Recuperação") {
        leadType = "recovery";
      }

      const stageDataItem = stageData[stageLabel];
      if (!stageDataItem) {
        console.error(`Stage data não encontrado para ${stageLabel}`);
        return;
      }

      const leads = stackedBarChartData.leads[leadType] || [];
      console.log("Leads:", leads);
      setCurrentLeadType(leadType);
      setSelectedLeads(leads);
      setModalTitle(`${datasetLabel} - ${stageLabel}`);
      setModalOpen(true);
    }
  };

  /**
   * Atualiza os leads selecionados no modal quando a lista de oportunidades muda.
   */
  useEffect(() => {
    if (selectedLeads.length > 0) {
      const updatedLeads = selectedLeads.filter((lead) => {
        const updatedLead = opportunities.find(
          (opp) => opp.crmId === lead.crmId
        );
        if (!updatedLead) return false;

        let stillInCurrentType = false;

        if (currentLeadType === "sales") {
          const inRecovery = isInRecovery(updatedLead);
          const recoveredAndInSales = hasRecoveredAndInSales(updatedLead);
          const inSalesFunnel = updatedLead.pipelineId === "56894";
          stillInCurrentType =
            !inRecovery && (recoveredAndInSales || inSalesFunnel);
        } else if (currentLeadType === "nonSales") {
          stillInCurrentType = updatedLead.stageId === "488514";
        } else if (currentLeadType === "notDistributed") {
          stillInCurrentType = !!(
            typeof updatedLead.startedInStage !== "string" &&
            updatedLead.startedInStage &&
            updatedLead.stageId === String(updatedLead.startedInStage.id)
          );
        } else if (currentLeadType === "recovery") {
          stillInCurrentType = isInRecovery(updatedLead);
        }

        return stillInCurrentType;
      });

      if (updatedLeads.length !== selectedLeads.length) {
        setSelectedLeads(updatedLeads);
      }
    }
  }, [opportunities, selectedLeads, currentLeadType]);

  // Verifica se os dados necessários estão disponíveis
  if (!stackedBarChartData) return <div>Carregando...</div>;

  return (
    <div className="w-full h-full flex flex-col">
      {/* Seletor de Intervalo de Datas */}
      <div className="mb-2 flex justify-end">
        <select
          value={dateRange}
          onChange={(e) => setDateRange(e.target.value)}
          className="p-2 border border-gray-300 rounded-md bg-white dark:bg-gray-700 dark:border-gray-600 text-sm"
        >
          <option value="last30Days">Últimos 30 dias</option>
          <option value="last15Days">Últimos 15 dias</option>
          <option value="last7Days">Últimos 7 dias</option>
          <option value="today">Hoje</option>
          <option value="thisWeek">Esta Semana</option>
          <option value="thisMonth">Este Mês</option>
          <option value="total">Total</option>
        </select>
      </div>

      {/* Gráfico de Barras Empilhadas */}
      <div className="relative flex-1">
        <Bar
          ref={chartRef}
          data={chartData}
          options={options}
          onClick={handleChartClick}
        />
      </div>

      {/* Modal para exibir leads selecionados */}
      {modalOpen && (
        <div
          className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50"
          onClick={() => setModalOpen(false)}
        >
          <div
            className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-full max-w-3xl mx-4 sm:mx-auto overflow-x-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold dark:text-white">
                {modalTitle}
              </h2>
              <button
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                onClick={() => setModalOpen(false)}
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
            <div className="max-h-[80vh] overflow-y-auto overflow-x-hidden px-4">
              <ul>
                {selectedLeads.map((lead) => (
                  <LeadCard key={lead.id} lead={lead} modalTitle={modalTitle} />
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StackedBarChart;
