// frontend/store/opportunityStore.ts
/* eslint-disable */

import {
  InitialChartData,
  Opportunity,
  StackedBarChartData,
} from "@/types/crm";
import { create } from "zustand";
import axios from "axios";
import { toast } from "react-toastify";

const BASE_URL = "https://api.form-dash.registrese.app.br/formDash";

const getHeaders = (token: string) => ({
  headers: { Authorization: `Bearer ${token}` },
});

export interface OpportunityStore {
  opportunities: Opportunity[];
  inProgressOpportunities: Opportunity[];
  completedOpportunities: Opportunity[];
  initialChartData: InitialChartData | null;
  stackedBarChartData: StackedBarChartData | null;
  isLoadingOpportunities: boolean;
  isLoadingInProgress: boolean;
  isLoadingCompleted: boolean;
  isLoadingInitialChartData: boolean;
  isLoadingStackedBarChartData: boolean;
  errorOpportunities: string | null;
  errorInProgress: string | null;
  errorCompleted: string | null;
  errorInitialChartData: string | null;
  errorStackedBarChartData: string | null;
  setOpportunities: (opps: Opportunity[]) => void;
  setInProgressOpportunities: (opps: Opportunity[]) => void;
  setCompletedOpportunities: (opps: Opportunity[]) => void;
  setInitialChartData: (data: InitialChartData) => void;
  setStackedBarChartData: (data: StackedBarChartData) => void;
  updateOpportunity: (updatedOpp: Opportunity) => void;
  fetchOpportunities: (token: string) => Promise<void>;
  fetchInProgressOpportunities: (token: string) => Promise<void>;
  fetchCompletedOpportunities: (token: string) => Promise<void>;
  fetchInitialChartData: (token: string) => Promise<void>;
  fetchStackedBarChartData: (token: string) => Promise<void>;
  handleError: (field: string, message: string, error: unknown) => void;
}

export const useOpportunityStore = create<OpportunityStore>((set, get) => ({
  opportunities: [],
  inProgressOpportunities: [],
  completedOpportunities: [],
  initialChartData: null,
  stackedBarChartData: null,
  isLoadingOpportunities: false,
  isLoadingInProgress: false,
  isLoadingCompleted: false,
  isLoadingInitialChartData: false,
  isLoadingStackedBarChartData: false,
  errorOpportunities: null,
  errorInProgress: null,
  errorCompleted: null,
  errorInitialChartData: null,
  errorStackedBarChartData: null,

  setOpportunities: (opps) => set({ opportunities: opps }),
  setInProgressOpportunities: (opps) => set({ inProgressOpportunities: opps }),
  setCompletedOpportunities: (opps) => set({ completedOpportunities: opps }),
  setInitialChartData: (data) => set({ initialChartData: data }),
  setStackedBarChartData: (data) => set({ stackedBarChartData: data }),

  updateOpportunity: (updatedOpp: Opportunity) =>
    set((state) => ({
      opportunities: state.opportunities.map((opp) =>
        opp.crmId === updatedOpp.crmId ? updatedOpp : opp
      ),
      inProgressOpportunities: state.inProgressOpportunities.map((opp) =>
        opp.crmId === updatedOpp.crmId ? updatedOpp : opp
      ),
      completedOpportunities: state.completedOpportunities.map((opp) =>
        opp.crmId === updatedOpp.crmId ? updatedOpp : opp
      ),
    })),

  handleError: (field: string, message: string, error: unknown) => {
    console.error(`${message} for ${field}:`, error);
    set({
      [`isLoading${field}`]: false,
      [`error${field}`]: message,
    } as any); // Usando 'as any' para acessar dinamicamente as chaves
    toast.error(message);
  },

  fetchOpportunities: async (token: string) => {
    console.log("Iniciando fetch de oportunidades...");
    set({ isLoadingOpportunities: true, errorOpportunities: null });

    try {
      const response = await axios.get(
        `${BASE_URL}/formStatus`,
        getHeaders(token)
      );
      const opportunities: Opportunity[] = response.data.data;
      console.log("Total de oportunidades carregadas:", opportunities.length);
      set({ opportunities, isLoadingOpportunities: false });
    } catch (error) {
      get().handleError(
        "Opportunities",
        "Erro ao buscar oportunidades.",
        error
      );
    }
  },

  fetchInProgressOpportunities: async (token: string) => {
    console.log("Iniciando fetch de oportunidades em progresso...");
    set({ isLoadingInProgress: true, errorInProgress: null });

    try {
      const response = await axios.get(
        `${BASE_URL}/inProgressOpportunities`,
        getHeaders(token)
      );
      const inProgressOpportunities: Opportunity[] = response.data.data;
      console.log(
        "Total de oportunidades em progresso:",
        inProgressOpportunities.length
      );
      set({ inProgressOpportunities, isLoadingInProgress: false });
    } catch (error) {
      get().handleError(
        "InProgress",
        "Erro ao buscar oportunidades em progresso.",
        error
      );
    }
  },

  fetchCompletedOpportunities: async (token: string) => {
    console.log("Iniciando fetch de oportunidades completas...");
    set({ isLoadingCompleted: true, errorCompleted: null });

    try {
      const response = await axios.get(
        `${BASE_URL}/completedFormsOpportunities`,
        getHeaders(token)
      );
      const completedOpportunities: Opportunity[] = response.data.data;
      console.log(
        "Total de oportunidades completas:",
        completedOpportunities.length
      );
      set({ completedOpportunities, isLoadingCompleted: false });
    } catch (error) {
      get().handleError(
        "Completed",
        "Erro ao buscar oportunidades completas.",
        error
      );
    }
  },

  fetchInitialChartData: async (token: string) => {
    console.log("Iniciando fetch de dados do InitialChart...");
    set({ isLoadingInitialChartData: true, errorInitialChartData: null });

    try {
      const response = await axios.get(
        `${BASE_URL}/initial-chart`,
        getHeaders(token)
      );
      const initialChartData: InitialChartData = response.data.data;
      console.log("Dados do InitialChart carregados:", initialChartData);
      set({ initialChartData, isLoadingInitialChartData: false });
    } catch (error) {
      get().handleError(
        "InitialChartData",
        "Erro ao buscar dados do InitialChart.",
        error
      );
    }
  },

  fetchStackedBarChartData: async (token: string) => {
    console.log("Iniciando fetch de dados do StackedBarChart...");
    set({ isLoadingStackedBarChartData: true, errorStackedBarChartData: null });

    try {
      const response = await axios.get(
        `${BASE_URL}/stacked-bar-chart`,
        getHeaders(token)
      );
      const stackedBarChartData: StackedBarChartData = response.data.data;

      set({ stackedBarChartData, isLoadingStackedBarChartData: false });
    } catch (error) {
      get().handleError(
        "StackedBarChartData",
        "Erro ao buscar dados do StackedBarChart.",
        error
      );
    }
  },
}));

export default useOpportunityStore;
