const CACHE_PREFIX = 'form_dash_';
const CACHE_VERSION = 'v1';
const MAX_CACHE_AGE = 24 * 60 * 60 * 1000; // 24 horas
const MAX_CACHE_SIZE = 4.5 * 1024 * 1024; // 4.5MB (abaixo do limite de 5MB)
const CACHE_EXPIRATION = 5 * 60 * 1000; // 5 minutos em milissegundos

interface CacheItem<T> {
  value: T;
  timestamp: number;
  size: number;
  expiration?: number;
}

class CacheService {
  private getCacheKey(key: string): string {
    return `${CACHE_PREFIX}${CACHE_VERSION}_${key}`;
  }

  private calculateSize(value: any): number {
    try {
      return new Blob([JSON.stringify(value)]).size;
    } catch {
      return 0;
    }
  }

  private async cleanOldCache(): Promise<void> {
    try {
      const now = Date.now();
      const keys = Object.keys(localStorage);
      let totalSize = 0;
      const cacheItems: { key: string; item: CacheItem<any> }[] = [];

      // Coleta informações sobre todos os itens do cache
      for (const key of keys) {
        if (key.startsWith(CACHE_PREFIX)) {
          try {
            const item = JSON.parse(localStorage.getItem(key) || '');
            totalSize += item.size || 0;
            cacheItems.push({ key, item });
          } catch {
            localStorage.removeItem(key);
          }
        }
      }

      // Remove itens expirados e ordena por timestamp
      cacheItems
        .filter(({ item }) => {
          const expirationTime = item.expiration || MAX_CACHE_AGE;
          return now - item.timestamp > expirationTime;
        })
        .forEach(({ key }) => {
          localStorage.removeItem(key);
          totalSize -= cacheItems.find(i => i.key === key)?.item.size || 0;
        });

      // Se ainda estiver acima do limite, remove os itens mais antigos
      if (totalSize > MAX_CACHE_SIZE) {
        cacheItems
          .sort((a, b) => a.item.timestamp - b.item.timestamp)
          .forEach(({ key, item }) => {
            if (totalSize > MAX_CACHE_SIZE) {
              localStorage.removeItem(key);
              totalSize -= item.size;
            }
          });
      }
    } catch (error) {
      console.error('Erro ao limpar cache:', error);
      this.clearAll();
    }
  }

  async set<T>(key: string, value: T, expiration: number = MAX_CACHE_AGE): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(key);
      const size = this.calculateSize(value);

      if (size > MAX_CACHE_SIZE) {
        console.warn('Item muito grande para cache:', key);
        return;
      }

      await this.cleanOldCache();

      const cacheItem: CacheItem<T> = {
        value,
        timestamp: Date.now(),
        size,
        expiration
      };

      localStorage.setItem(cacheKey, JSON.stringify(cacheItem));
    } catch (error) {
      console.error('Erro ao salvar no cache:', error);
      this.clearAll();
    }
  }

  get<T>(key: string): T | null {
    try {
      const cacheKey = this.getCacheKey(key);
      const item = localStorage.getItem(cacheKey);
      
      if (!item) return null;

      const cacheItem: CacheItem<T> = JSON.parse(item);
      const expirationTime = cacheItem.expiration || MAX_CACHE_AGE;
      
      if (Date.now() - cacheItem.timestamp > expirationTime) {
        localStorage.removeItem(cacheKey);
        return null;
      }

      return cacheItem.value;
    } catch {
      return null;
    }
  }

  clearAll(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(CACHE_PREFIX)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('Erro ao limpar cache:', error);
    }
  }
}

export const cacheService = new CacheService(); 