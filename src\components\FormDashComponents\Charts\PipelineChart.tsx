import React, { useState } from "react";
import { Pie } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  ArcElement,
  Toolt<PERSON>,
  Legend,
} from "chart.js";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { FaCalendarDay, FaEllipsisV, FaRegCalendarAlt } from "react-icons/fa"; // Ícone de 3 bolinhas verticais

ChartJS.register(CategoryScale, LinearScale, ArcElement, Tooltip, Legend);

interface Opportunity {
  pipeline: string;
  isCompleted: boolean;
  createdAt: string;
}

interface PipelineChartProps {
  opportunities: Opportunity[];
  darkMode: boolean;
}

const pipelineStageMapping: Record<string, string> = {
  "dba187ff-8164-4d16-a385-b08d1409896a": "MEIS",
  "d2fe08c2-03a5-4cce-ba93-49d3b20918c6": "Somente consultar",
  "619cb5e2-bdc1-4ce3-b128-d1f547487a58": "Entrada - Form Incompleto",
  "bf7b8624-5e28-47e3-b8a7-6ef2d5d3eaca": "Distribuir pra Vendas",
  "29a1eca5-93fb-4d44-ad11-ef12293d9a97": "MQL",
  "f668bca1-f0d8-40a7-8bf7-ecf91f34a91e": "MQL",
  "68f9a9be-22b6-4be4-9ea8-e59542cb5993": "MQL",
  "da824ed6-15ea-4099-87a0-eafdd542e0cd": "Menor que 100",
  "351704a7-da75-4872-b73f-f27e3fa4e412": "MQL",
  "f4eaa1e8-1761-4ba5-9921-f28dd6b13ebf": "MQL",
  "df73c8aa-8047-445c-8f79-abffc006ba4e": "MQL",
  "e3059d23-6294-459b-915b-377b6cb4a5e3": "MQL",
};

const extractHashFromPipelineUrl = (pipelineUrl: string): string | null => {
  const hash = pipelineUrl.split("?hash=")[1];
  return hash ? hash : null;
};

const getPipelineName = (hash: string | null, isCompleted: boolean): string => {
  if (!hash && !isCompleted) {
    return "Entrada - Form Incompleto";
  }
  return hash ? pipelineStageMapping[hash] || "Outro" : "Outro";
};

const PipelineChart: React.FC<PipelineChartProps> = ({
  opportunities,
  darkMode,
}) => {
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);
  const [isPeriod, setIsPeriod] = useState<boolean>(false); // Controla se é período ou data única
  const [isDate, setIsDate] = useState<boolean>(false); // Controla se é período ou data única

  const handleFilter = (opportunityDate: string) => {
    const oppDate = new Date(opportunityDate);
    if (!startDate && !endDate) return true;
    if (startDate && endDate) return oppDate >= startDate && oppDate <= endDate;
    if (startDate) return oppDate >= startDate;
    if (endDate) return oppDate <= endDate;
    return true;
  };

  const filteredOpportunities = opportunities.filter((opp) =>
    handleFilter(opp.createdAt)
  );

  const pipelines = filteredOpportunities.map((opp) => {
    const hash = opp.pipeline ? extractHashFromPipelineUrl(opp.pipeline) : null;
    return getPipelineName(hash, opp.isCompleted);
  });

  const pipelineCounts = pipelines.reduce(
    (acc: Record<string, number>, pipeline) => {
      acc[pipeline] = (acc[pipeline] || 0) + 1;
      return acc;
    },
    {}
  );

  const data = {
    labels: Object.keys(pipelineCounts),
    datasets: [
      {
        label: "Formulários por Pipeline",
        data: Object.values(pipelineCounts),
        backgroundColor: [
          "#FF6384",
          "#36A2EB",
          "#FFCE56",
          "#4BC0C0",
          "#9966FF",
          "#FF9F40",
          "#C9CBCF",
        ],
        borderColor: "#fff",
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
      },
      title: {
        display: true,
        text: "Distribuição de Formulários por Pipeline",
      },
    },
  };

  return (
    <div>
      {/* Gráfico */}
      <div style={{ height: "300px" }}>
        {pipelines.length > 0 ? (
          <Pie data={data} options={options} />
        ) : (
          <p className="text-center text-gray-500">Nenhum resultado</p>
        )}
      </div>

      {/* Ícone de menu */}
      <div className="relative mt-4 flex justify-end">
        <button
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          className={`p-2 rounded-full transition duration-300 hover:scale-110 ${
            darkMode ? "text-gray-400" : "text-black"
          }`}
        >
          <FaEllipsisV size={20} />
        </button>

        {/* Menu suspenso */}
        {isMenuOpen && (
          <div
            className={`absolute z-10 top-10 right-0 w-40 rounded-md shadow-lg transition-all duration-300 ${
              darkMode ? "bg-gray-700 text-white" : "bg-white text-black"
            }`}
          >
            <ul className="py-1">
              <li
                className={`cursor-pointer flex items-center px-4 py-2 hover:bg-gray-600 ${
                  darkMode
                    ? "hover:bg-gray-600 text-white"
                    : "hover:bg-gray-100 text-black"
                }`}
                onClick={() => {
                  setIsPeriod(false);
                  setIsDate(true);
                  setIsMenuOpen(false);
                }}
              >
                <FaCalendarDay className="mr-2" /> Data
              </li>
              <li
                className={`cursor-pointer flex items-center px-4 py-2 hover:bg-gray-600 ${
                  darkMode
                    ? "hover:bg-gray-600 text-white"
                    : "hover:bg-gray-100 text-black"
                }`}
                onClick={() => {
                  setIsPeriod(true);
                  setIsDate(false);
                  setIsMenuOpen(false);
                }}
              >
                <FaRegCalendarAlt className="mr-2" /> Período
              </li>
            </ul>
          </div>
        )}
      </div>

      {/* Filtros de data/período */}
      <div className="mt-4">
        {!isPeriod && isDate && (
          <DatePicker
            selected={startDate}
            onChange={(date: Date | null) => setStartDate(date as Date)}
            placeholderText="Escolher Data"
            className={`p-2 border rounded focus:outline-none ${
              darkMode
                ? "bg-gray-700 text-white border-gray-500"
                : "bg-white text-black border-gray-300"
            }`}
            dateFormat="dd/MM/yyyy"
          />
        )}

        {isPeriod && (
          <div className="flex space-x-4">
            <DatePicker
              selected={startDate}
              onChange={(date: Date | null) => setStartDate(date as Date)}
              selectsStart
              startDate={startDate}
              endDate={endDate}
              placeholderText="Data Inicial"
              className={`p-2 border rounded focus:outline-none ${
                darkMode
                  ? "bg-gray-700 text-white border-gray-500"
                  : "bg-white text-black border-gray-300"
              }`}
              dateFormat="dd/MM/yyyy"
            />

            <DatePicker
              selected={endDate}
              onChange={(date: Date | null) => setEndDate(date as Date)}
              selectsEnd
              startDate={startDate}
              endDate={endDate}
              placeholderText="Data Final"
              className={`p-2 border rounded focus:outline-none ${
                darkMode
                  ? "bg-gray-700 text-white border-gray-500"
                  : "bg-white text-black border-gray-300"
              }`}
              dateFormat="dd/MM/yyyy"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default PipelineChart;
