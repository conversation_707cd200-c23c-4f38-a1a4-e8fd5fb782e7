import React, { useState, useMemo, useRef, useEffect } from "react";
import {
  FaEnvelope,
  FaPhone,
  FaUser,
  FaTimesCircle,
  FaStar,
  FaMapMarkerAlt,
  FaBuilding,
  FaCalendarAlt,
  FaClock,
  FaMoneyBillWave,
  FaTag,
  FaEllipsisV,
  FaUserPlus,
  FaExternalLinkAlt,
  FaStopwatch,
  FaRandom,
  FaChevronDown,
  FaChevronUp
} from "react-icons/fa";
import SideModal from "./SideModal";
import ConfirmDeleteModal from "@/components/FormDashComponents/Components/DeleteConfirmModal";
import { toast } from "react-toastify";
import axios from "axios";
import { useFormDataModalStore } from "@/context/useFormDataModalStore";
import { Portal } from "@/components/Portal";
import Image from "next/image";
import Link from "next/link";
import { differenceInMinutes, parseISO, format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface FormDataType {
  [key: string]: string | number | boolean | string[] | null | undefined;
}

interface VendedorFormDataModalProps {
  vendedores?: Array<{
    ownerId: number;
    nome: string;
    avatar: string;
  }>;
  currentVendedor?: {
    ownerId: number;
    nome: string;
    avatar: string;
  };
  onDelegate?: (toVendedorId: number) => void;
  onUpdate?: () => Promise<void>;
}

const VendedorFormDataModal: React.FC<VendedorFormDataModalProps> = ({
  vendedores = [],
  currentVendedor,
  onDelegate,
  onUpdate
}) => {
  const { selectedFormData, closeFormDataModal, isFormDataModalOpen } = useFormDataModalStore();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const [showDelegateMenu, setShowDelegateMenu] = useState(false);
  const menuButtonRef = useRef<HTMLButtonElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const [expandedSections, setExpandedSections] = useState<{
    business: boolean;
    financial: boolean;
    marketing: boolean;
  }>({
    business: false,
    financial: false,
    marketing: false
  });

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
        setShowDelegateMenu(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const parsedFormData = useMemo(() => {
    try {
      return typeof selectedFormData === "string" ? JSON.parse(selectedFormData) : selectedFormData;
    } catch (error) {
      console.error("Erro ao parsear selectedFormData:", error);
      return {};
    }
  }, [selectedFormData]);

  const {
    crmId,
    leadName,
    leadMail,
    leadNumber,
    brandTitle,
    score,
  } = parsedFormData;

  // Função para calcular a cor do score
  const getScoreColor = (score: number | undefined) => {
    if (score === undefined) return "text-gray-400";
    if (score <= 80) return "text-red-500";
    if (score <= 160) return "text-yellow-500";
    if (score <= 220) return "text-cyan-500";
    if (score <= 300) return "text-green-500";
    return "text-blue-500";
  };

  // Função para excluir a oportunidade
  const handleDeleteOpportunity = async () => {
    if (!crmId) return;
    try {
      const response = await axios.put(
        `https://api.form-dash.registrese.app.br/opportunities/delete/${crmId}`
      );
      if (response.status === 200 && response.data.success) {
        toast.success("Oportunidade excluída com sucesso!");
        closeFormDataModal();
      } else {
        throw new Error("Erro ao excluir oportunidade");
      }
    } catch (error) {
      console.error("Erro ao excluir oportunidade:", error);
      toast.error("Erro ao excluir oportunidade, tente novamente.");
    }
  };

  // Função para formatar data
  const formatDate = (date: string | null | undefined) => {
    if (!date) return "-";
    try {
      return new Date(date).toLocaleString("pt-BR");
    } catch {
      return date;
    }
  };

  // Função para formatar valor monetário
  const formatCurrency = (value: number | null | undefined) => {
    if (value === null || value === undefined) return "-";
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  };

  // Função para calcular o tempo de resposta
  const getResponseTime = (dataCadastro?: string, entradaLead?: string) => {
    if (!dataCadastro || !entradaLead) return null;
    try {
      const cadastroDate = parseISO(dataCadastro);
      const entradaDate = parseISO(entradaLead);
      const diffInMinutes = differenceInMinutes(entradaDate, cadastroDate);
      
      if (diffInMinutes < 60) {
        return `${diffInMinutes} minutos`;
      } else {
        const hours = Math.floor(diffInMinutes / 60);
        const minutes = diffInMinutes % 60;
        return `${hours}h${minutes > 0 ? ` ${minutes}min` : ''}`;
      }
    } catch {
      return null;
    }
  };

  const reatribuirLead = async (crmId: number | undefined) => {
    if (!crmId) return;
    try {
      const response = await fetch(`/api/leads/reatribuir/${crmId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Falha ao reatribuir lead');
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error('Falha ao reatribuir lead');
      }

      toast.success("Lead reatribuído com sucesso!");
      closeFormDataModal();
      
      if (onUpdate) {
        await onUpdate();
      }
    } catch (error) {
      console.error("Erro ao reatribuir lead:", error);
      toast.error("Erro ao reatribuir lead, tente novamente mais tarde.");
    }
  };

  // Função para verificar se um valor existe e não está vazio
  const hasValue = (value: any) => {
    if (value === null || value === undefined) return false;
    if (typeof value === 'string' && value.trim() === '') return false;
    return true;
  };

  return (
    <SideModal
      isOpen={isFormDataModalOpen}
      onClose={closeFormDataModal}
      header={
        <div className="flex items-center justify-between w-full">
          <h2 className="text-2xl font-bold">Detalhes do Lead</h2>
          <div className="relative" ref={menuRef}>
            <button
              ref={menuButtonRef}
              onClick={(e) => {
                e.stopPropagation();
                setShowMenu(!showMenu);
                setShowDelegateMenu(false);
              }}
              className="p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <FaEllipsisV size={16} />
            </button>

            {/* Menu de Opções */}
            {showMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 z-[100]">
                <div className="py-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowDelegateMenu(true);
                      setShowMenu(false);
                    }}
                    className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 w-full text-left"
                  >
                    <FaUserPlus size={14} />
                    Delegar Lead
                  </button>

                  {/* Novo item: Reatribuir Lead */}
                  <button
                    onClick={async (e) => {
                      e.stopPropagation();
                      setShowMenu(false);
                      try {
                        await reatribuirLead(parsedFormData.crmId);
                        if (onUpdate) {
                          await onUpdate();
                        }
                      } catch (error) {
                        console.error("Erro ao reatribuir lead:", error);
                      }
                    }}
                    className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 w-full text-left"
                  >
                    <FaRandom size={14} />
                    Reatribuir Lead
                  </button>

                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsDeleteModalOpen(true);
                      setShowMenu(false);
                    }}
                    className="flex items-center gap-2 px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 w-full text-left"
                  >
                    <FaTimesCircle size={14} />
                    Excluir Lead
                  </button>
                </div>
              </div>
            )}

            {/* Menu de Delegação */}
            {showDelegateMenu && (
              <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 z-[100]">
                <div className="py-2">
                  <div className="px-4 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase">
                    Delegar para
                  </div>
                  <div className="max-h-64 overflow-y-auto">
                    {vendedores
                      .filter((v) => v.ownerId !== currentVendedor?.ownerId)
                      .map((vendedor) => (
                        <button
                          key={vendedor.ownerId}
                          onClick={(e) => {
                            e.stopPropagation();
                            onDelegate?.(vendedor.ownerId);
                            setShowDelegateMenu(false);
                          }}
                          className="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 w-full"
                        >
                          <div className="relative w-6 h-6 rounded-full overflow-hidden">
                            <Image
                              src={vendedor.avatar || "/default-avatar.png"}
                              alt={vendedor.nome}
                              fill
                              sizes="24px"
                              className="object-cover"
                            />
                          </div>
                          <span className="flex-1 truncate">
                            {vendedor.nome}
                          </span>
                        </button>
                      ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      }
    >
      <div className="flex flex-col h-full overflow-hidden">
        {/* Container rolável para todo o conteúdo */}
        <div className="flex-1 overflow-y-auto">
          {/* Cabeçalho com Informações Principais */}
          <div className="bg-gray-100 dark:bg-gray-800 p-5 m-4 rounded-lg shadow space-y-4">
            {/* Primeira linha: Marca, Score e CRM ID */}
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-xl md:text-2xl font-bold text-gray-900 dark:text-gray-100 uppercase break-words max-w-[400px]">
                  {brandTitle || "Marca não informada"}
                </h3>
                <Link
                  href={`https://app.pipe.run/pipeline/gerenciador/visualizar/${crmId}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 text-sm text-blue-500 hover:text-blue-600 mt-1"
                >
                  <span>#{crmId}</span>
                  <FaExternalLinkAlt size={12} />
                </Link>
              </div>
              <div
                className={`flex items-center text-xl ${getScoreColor(
                  score
                )} flex-shrink-0`}
              >
                <FaStar className="mr-2" />
                <span>{score || "N/A"}</span>
              </div>
            </div>

            {/* Segunda linha: Telefone e Hora entrada lead */}
            <div className="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-400">
              {parsedFormData.leadNumber && (
                <div className="flex items-center gap-2">
                  <FaPhone className="flex-shrink-0" />
                  <span>{parsedFormData.leadNumber}</span>
                </div>
              )}
              <div className="flex items-center gap-2">
                <FaCalendarAlt className="flex-shrink-0" />
                <span>
                  {parsedFormData.entradaLead
                    ? format(
                        parseISO(parsedFormData.entradaLead),
                        "dd/MM/yyyy 'às' HH:mm",
                        { locale: ptBR }
                      )
                    : "Data não informada"}
                </span>
              </div>
            </div>

            {/* Terceira linha: Vendedor e Etapa */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="relative w-8 h-8 rounded-full overflow-hidden ring-2 ring-blue-500/20">
                  <Image
                    src={currentVendedor?.avatar || "/default-avatar.png"}
                    alt={currentVendedor?.nome || "Vendedor"}
                    fill
                    sizes="32px"
                    className="object-cover"
                  />
                </div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {currentVendedor?.nome || "Vendedor não atribuído"}
                </span>
              </div>
              <span className="text-sm font-medium px-3 py-1 rounded-full bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300">
                {parsedFormData.businessInternship || "Etapa não definida"}
              </span>
            </div>
          </div>

          {/* Informações Detalhadas */}
          <div className="px-4 mb-20">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden border border-gray-200 dark:border-gray-700">
              {/* Informações do Negócio */}
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="text-lg font-semibold flex items-center text-gray-900 dark:text-gray-100">
                    <FaBuilding className="mr-2" />
                    Informações do Negócio
                  </h4>
                  <button
                    onClick={() => setExpandedSections(prev => ({ ...prev, business: !prev.business }))}
                    className="text-blue-500 hover:text-blue-600 flex items-center gap-1 text-sm"
                  >
                    {expandedSections.business ? (
                      <>
                        <span>Menos detalhes</span>
                        <FaChevronUp />
                      </>
                    ) : (
                      <>
                        <span>Mais detalhes</span>
                        <FaChevronDown />
                      </>
                    )}
                  </button>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  {/* Campos principais sempre visíveis */}
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Tipo de Negócio
                    </p>
                    <p className="text-gray-900 dark:text-gray-100">
                      {parsedFormData.tipoNegocio || "-"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Ramo de Atuação
                    </p>
                    <p className="text-gray-900 dark:text-gray-100">
                      {parsedFormData.ramoAtuacao || "-"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Possui CNPJ
                    </p>
                    <p className="text-gray-900 dark:text-gray-100">
                      {parsedFormData.temCNPJ || "-"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Site e/ou Redes Sociais
                    </p>
                    <p className="text-gray-900 dark:text-gray-100">
                      {parsedFormData.redesSociaisSite || "-"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Colaboradores
                    </p>
                    <p className="text-gray-900 dark:text-gray-100">
                      {parsedFormData.colaboradores || "-"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Porte
                    </p>
                    <p className="text-gray-900 dark:text-gray-100">
                      {parsedFormData.porte || "-"}
                    </p>
                  </div>

                  {/* Campos extras que aparecem quando expandido */}
                  {expandedSections.business && (
                    <>
                      {hasValue(parsedFormData.comoFoiAtendido) && (
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Como Foi Atendido
                          </p>
                          <p className="text-gray-900 dark:text-gray-100">
                            {parsedFormData.comoFoiAtendido}
                          </p>
                        </div>
                      )}
                      {hasValue(parsedFormData.protocolo) && (
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Protocolo
                          </p>
                          <p className="text-gray-900 dark:text-gray-100">
                            {parsedFormData.protocolo}
                          </p>
                        </div>
                      )}
                      {hasValue(parsedFormData.viabilidade) && (
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Viabilidade
                          </p>
                          <p className="text-gray-900 dark:text-gray-100">
                            {parsedFormData.viabilidade}
                          </p>
                        </div>
                      )}
                      {hasValue(parsedFormData.plano) && (
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Plano
                          </p>
                          <p className="text-gray-900 dark:text-gray-100">
                            {parsedFormData.plano}
                          </p>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>

              {/* Informações Financeiras */}
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="text-lg font-semibold flex items-center text-gray-900 dark:text-gray-100">
                    <FaMoneyBillWave className="mr-2" />
                    Informações Financeiras
                  </h4>
                  <button
                    onClick={() => setExpandedSections(prev => ({ ...prev, financial: !prev.financial }))}
                    className="text-blue-500 hover:text-blue-600 flex items-center gap-1 text-sm"
                  >
                    {expandedSections.financial ? (
                      <>
                        <span>Menos detalhes</span>
                        <FaChevronUp />
                      </>
                    ) : (
                      <>
                        <span>Mais detalhes</span>
                        <FaChevronDown />
                      </>
                    )}
                  </button>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  {/* Campos principais sempre visíveis */}
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Valor
                    </p>
                    <p className="text-gray-900 dark:text-gray-100">
                      {formatCurrency(parsedFormData.valor)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Valor a Cobrar
                    </p>
                    <p className="text-gray-900 dark:text-gray-100">
                      {parsedFormData.valorCobrar || "-"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Forma de Pagamento
                    </p>
                    <p className="text-gray-900 dark:text-gray-100">
                      {parsedFormData.formaPagamento || "-"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Condição de Pagamento
                    </p>
                    <p className="text-gray-900 dark:text-gray-100">
                      {parsedFormData.condicaoPagamento || "-"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Parcelas
                    </p>
                    <p className="text-gray-900 dark:text-gray-100">
                      {parsedFormData.parcelas || "-"}
                    </p>
                  </div>

                  {/* Campos extras que aparecem quando expandido */}
                  {expandedSections.financial && (
                    <>
                      {hasValue(parsedFormData.valorDemaisParcelas) && (
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Valor das Demais Parcelas
                          </p>
                          <p className="text-gray-900 dark:text-gray-100">
                            {parsedFormData.valorDemaisParcelas}
                          </p>
                        </div>
                      )}
                      {hasValue(parsedFormData.primeiroVencimento) && (
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Primeiro Vencimento
                          </p>
                          <p className="text-gray-900 dark:text-gray-100">
                            {parsedFormData.primeiroVencimento}
                          </p>
                        </div>
                      )}
                      {hasValue(parsedFormData.analiseCredito) && (
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Análise de Crédito
                          </p>
                          <p className="text-gray-900 dark:text-gray-100">
                            {parsedFormData.analiseCredito}
                          </p>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>

              {/* Informações de Marketing */}
              <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="text-lg font-semibold flex items-center text-gray-900 dark:text-gray-100">
                    <FaTag className="mr-2" />
                    Informações de Marketing
                  </h4>
                  <button
                    onClick={() => setExpandedSections(prev => ({ ...prev, marketing: !prev.marketing }))}
                    className="text-blue-500 hover:text-blue-600 flex items-center gap-1 text-sm"
                  >
                    {expandedSections.marketing ? (
                      <>
                        <span>Menos detalhes</span>
                        <FaChevronUp />
                      </>
                    ) : (
                      <>
                        <span>Mais detalhes</span>
                        <FaChevronDown />
                      </>
                    )}
                  </button>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  {/* Campos principais sempre visíveis */}
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Origem
                    </p>
                    <p className="text-gray-900 dark:text-gray-100">
                      {parsedFormData.origem || "-"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Canal
                    </p>
                    <p className="text-gray-900 dark:text-gray-100">
                      {parsedFormData.canal || "-"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Campanha
                    </p>
                    <p className="text-gray-900 dark:text-gray-100">
                      {parsedFormData.campanha || "-"}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                      Histórico de Campanhas
                    </p>
                    <p className="text-gray-900 dark:text-gray-100">
                      {parsedFormData.historicoCampanhas || "-"}
                    </p>
                  </div>

                  {/* Campos extras que aparecem quando expandido */}
                  {expandedSections.marketing && (
                    <>
                      {hasValue(parsedFormData.dispositivo) && (
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Dispositivo
                          </p>
                          <p className="text-gray-900 dark:text-gray-100">
                            {parsedFormData.dispositivo}
                          </p>
                        </div>
                      )}
                      {hasValue(parsedFormData.urlConversao) && (
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            URL de Conversão
                          </p>
                          <p className="text-gray-900 dark:text-gray-100">
                            {parsedFormData.urlConversao}
                          </p>
                        </div>
                      )}
                      {hasValue(parsedFormData.utmFinal) && (
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            UTM Final
                          </p>
                          <p className="text-gray-900 dark:text-gray-100">
                            {parsedFormData.utmFinal}
                          </p>
                        </div>
                      )}
                      {hasValue(parsedFormData.horaVisita) && (
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Hora da Visita
                          </p>
                          <p className="text-gray-900 dark:text-gray-100">
                            {format(parseISO(parsedFormData.horaVisita), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}
                          </p>
                        </div>
                      )}
                      {hasValue(parsedFormData.horaConversao) && (
                        <div>
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            Hora da Conversão
                          </p>
                          <p className="text-gray-900 dark:text-gray-100">
                            {format(parseISO(parsedFormData.horaConversao), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}
                          </p>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Confirm Delete Modal */}
        <ConfirmDeleteModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          onConfirm={handleDeleteOpportunity}
          opportunityName={brandTitle || "Oportunidade"}
        />
      </div>
    </SideModal>
  );
};

export default VendedorFormDataModal; 
