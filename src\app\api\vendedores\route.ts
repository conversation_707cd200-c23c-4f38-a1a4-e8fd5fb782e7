import { NextResponse } from 'next/server';

const EXCLUDED_USER_IDS = [78004, 52245, 59369, 52223, 54263, 71078, 89895];

interface PipeRunUser {
  id: number;
  name: string;
  avatar: string;
  // ... outros campos que podem vir da API
}

interface VendedorResponse {
  ownerId: number;
  nome: string;
  avatar: string;
}

export async function GET() {
  try {
    const response = await fetch('https://api.pipe.run/v1/users?show=150', {
      headers: {
        'accept': 'application/json',
        'token': process.env.CRM_TOKEN || ''
      }
    });

    if (!response.ok) {
      throw new Error('Falha ao buscar vendedores');
    }

    const data = await response.json();

    // Filtra os usuários excluindo os IDs especificados e mapeia para o formato desejado
    const vendedores: VendedorResponse[] = data.data
      .filter((user: PipeRunUser) => !EXCLUDED_USER_IDS.includes(user.id))
      .map((user: PipeRunUser) => ({
        ownerId: user.id,
        nome: user.name,
        avatar: user.avatar
      }));
      console.log('Vendedores:', vendedores);
    return NextResponse.json({ 
      success: true, 
      data: vendedores 
    });

  } catch (error) {
    console.error('Erro ao buscar vendedores:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Erro ao buscar vendedores' 
      },
      { status: 500 }
    );
  }
} 