import { NextResponse } from 'next/server';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const crmId = params.id;
    
    // Validar crmId
    if (!crmId || crmId === 'undefined' || crmId === 'null') {
      return NextResponse.json({ error: 'ID inválido' }, { status: 400 });
    }
    
    const crmToken = process.env.CRM_TOKEN;

    if (!crmToken) {
      console.error('CRM_TOKEN não configurado');
      return NextResponse.json(
        { error: 'Configuração do servidor incompleta' },
        { status: 500 }
      );
    }

    console.log(`Buscando dados do CRM para ID: ${crmId}`);
    
    const response = await fetch(
      `https://api.pipe.run/v1/deals/${crmId}?with=customFields`,
      {
        headers: {
          'accept': 'application/json',
          'token': crmToken,
        },
      }
    );
    
    // Retornar o 404 diretamente se não encontrar o Deal
    if (response.status === 404) {
      console.log(`Deal ID ${crmId} não encontrado na API do CRM`);
      return NextResponse.json(
        { error: 'Deal não encontrado' },
        { status: 404 }
      );
    }

    if (!response.ok) {
      console.error(`Erro da API do CRM: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: `Erro ao acessar API do CRM: ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Erro ao buscar dados do CRM:', error);
    return NextResponse.json(
      { error: 'Falha ao buscar dados do CRM' },
      { status: 500 }
    );
  }
} 