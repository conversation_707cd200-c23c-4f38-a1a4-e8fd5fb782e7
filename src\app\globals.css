@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --radius: 0.5rem;
  }
}
/* Estilizando a barra de rolagem */
::-webkit-scrollbar {
  width: 8px; /* Largura da barra de rolagem */
}

::-webkit-scrollbar-track {
  background: transparent; /* <PERSON><PERSON> da barra (gray-100 do Tailwind) */
}

::-webkit-scrollbar-thumb {
  background-color: #6b7280; /* Cor do polegar (gray-500 do Tailwind) */
  border-radius: 9999px; /* Borda arredondada */
  border: 2px solid #e5e7eb; /* Espaçamento entre o polegar e a barra */
}

/* Ribbon styles */
.ribbon-perdido, .ribbon-ganho {
  position: absolute;
  left: -5px;
  top: -5px;
  z-index: 1;
  overflow: hidden;
  width: 75px;
  height: 75px;
  text-align: right;
  pointer-events: none;
}

.ribbon-perdido span, .ribbon-ganho span {
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
  text-align: center;
  line-height: 20px;
  transform: rotate(-45deg);
  width: 100px;
  display: block;
  background: linear-gradient(#dc2626 0%, #b91c1c 100%);
  box-shadow: 0 3px 10px -5px rgba(0, 0, 0, 1);
  position: absolute;
  top: 19px;
  left: -21px;
}

.ribbon-ganho span {
  background: linear-gradient(#22c55e 0%, #16a34a 100%);
}

.ribbon-perdido span::before, .ribbon-ganho span::before {
  content: "";
  position: absolute;
  left: 0px;
  top: 100%;
  z-index: -1;
  border-left: 3px solid #831843;
  border-right: 3px solid transparent;
  border-bottom: 3px solid transparent;
  border-top: 3px solid #831843;
}

.ribbon-ganho span::before {
  border-left-color: #166534;
  border-top-color: #166534;
}

.ribbon-perdido span::after, .ribbon-ganho span::after {
  content: "";
  position: absolute;
  right: 0px;
  top: 100%;
  z-index: -1;
  border-left: 3px solid transparent;
  border-right: 3px solid #831843;
  border-bottom: 3px solid transparent;
  border-top: 3px solid #831843;
}

.ribbon-ganho span::after {
  border-right-color: #166534;
  border-top-color: #166534;
}

/* Estilos personalizados para o DatePicker */
.react-datepicker-wrapper {
  @apply w-auto;
}

.react-datepicker {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg !important;
  font-family: inherit !important;
}

.react-datepicker__triangle {
  display: none;
}

.react-datepicker__header {
  @apply bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 !important;
  padding-top: 0.75rem !important;
}

.react-datepicker__month-container {
  @apply float-none !important;
}

.react-datepicker__current-month {
  @apply text-gray-800 dark:text-gray-200 font-semibold mb-2 !important;
}

.react-datepicker__day-names {
  @apply mt-2 !important;
}

.react-datepicker__day-name {
  @apply text-gray-600 dark:text-gray-400 font-medium !important;
  width: 2rem !important;
  line-height: 2rem !important;
  margin: 0.2rem !important;
}

.react-datepicker__day {
  @apply text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md !important;
  width: 2rem !important;
  line-height: 2rem !important;
  margin: 0.2rem !important;
}

.react-datepicker__day--selected {
  @apply bg-blue-500 text-white hover:bg-blue-600 !important;
}

.react-datepicker__day--keyboard-selected {
  @apply bg-blue-500/20 dark:bg-blue-500/40 !important;
}

.react-datepicker__day--disabled {
  @apply text-gray-400 dark:text-gray-600 hover:bg-transparent cursor-not-allowed !important;
}

.react-datepicker__navigation {
  @apply top-3 !important;
}

.react-datepicker__navigation-icon::before {
  @apply border-gray-400 dark:border-gray-500 !important;
}

.react-datepicker__day--outside-month {
  @apply text-gray-400 dark:text-gray-600 !important;
}

.react-datepicker__input-container {
  @apply w-auto !important;
}