import React, { useState, useEffect } from 'react';
import { MarketingLead } from '@/types/marketing';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { 
  ClockIcon, 
  LinkIcon, 
  EnvelopeIcon,
  UserIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  DocumentTextIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';

interface LeadCardProps {
  lead: MarketingLead;
}

const LeadCard: React.FC<LeadCardProps> = ({ lead }) => {
  const [showLocalStorage, setShowLocalStorage] = useState(false);
  const [brandName, setBrandName] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  // Verificar todas as possíveis fontes do ID do CRM
  const crmIdFromLocalStorage = lead.localStorageData?.['@crmId'] 
    ? String(lead.localStorageData['@crmId']) 
    : '';
  const crmIdFromLead = lead.crmId || '';
  
  // Usar o primeiro ID válido que encontrarmos, priorizando o do localStorageData
  const crmId = crmIdFromLocalStorage || crmIdFromLead;

  useEffect(() => {
    let isMounted = true;
    
    const loadBrandName = async () => {
      if (!crmId || isLoading) return;
      
      setIsLoading(true);
      try {
        const response = await fetch(`/api/crm/deal/${crmId}`);
        if (!response.ok) {
          throw new Error('Falha ao buscar dados do CRM');
        }

        const result = await response.json();
        
        // Verifica se temos os dados necessários
        if (!result?.data?.customFields || !Array.isArray(result.data.customFields)) {
          console.error('Dados do CRM inválidos:', result);
          return;
        }

        const brandField = result.data.customFields.find((field: any) => field.id === 213657);
        const name = brandField?.value || null;

        if (name && isMounted) {
          setBrandName(name);
        }
      } catch (error) {
        console.error('Erro ao buscar dados do CRM:', error);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };
    
    if (crmId) {
      loadBrandName();
    }
    
    return () => {
      isMounted = false;
    };
  }, [crmId]);

  const handleViewCRM = () => {
    if (crmId) {
      window.open(`https://app.pipe.run/pipeline/gerenciador/visualizar/${crmId}`, '_blank');
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden hover:border-indigo-500 dark:hover:border-indigo-400 transition-colors duration-200">
      <div className="p-4">
        {/* Cabeçalho com identificação */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            {lead.Opportunity?.leadName && (
              <div className="flex items-center gap-2">
                <UserIcon className="h-4 w-4 text-indigo-500" />
                <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {lead.Opportunity?.leadName}
                </h3>
              </div>
            )}
            {lead.utmMedium && (
              <div className="flex items-center gap-2 mt-1">
                <EnvelopeIcon className="h-4 w-4 text-indigo-500" />
                <span className="text-sm text-gray-600 dark:text-gray-300 truncate">
                  {lead.utmMedium}
                </span>
              </div>
            )}
            {crmId && (
              isLoading ? (
                <div className="flex items-center gap-2 mt-2">
                  <BuildingOfficeIcon className="h-4 w-4 text-indigo-500 animate-pulse" />
                  <span className="text-sm text-gray-400 dark:text-gray-500">
                    Carregando marca...
                  </span>
                </div>
              ) : brandName ? (
                <div className="mt-2 inline-flex items-center gap-2 px-2.5 py-1 rounded-full bg-indigo-50 dark:bg-indigo-900/30 border border-indigo-100 dark:border-indigo-800">
                  <BuildingOfficeIcon className="h-4 w-4 text-indigo-500" />
                  <span className="text-sm font-medium text-indigo-700 dark:text-indigo-300">
                    {brandName}
                  </span>
                </div>
              ) : null
            )}
          </div>
          {crmId && (
            <button
              onClick={handleViewCRM}
              className="ml-2 px-2 py-1 text-xs font-medium text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/50 rounded-md hover:bg-indigo-100 dark:hover:bg-indigo-900 transition-colors"
            >
              CRM #{crmId}
            </button>
          )}
        </div>

        {/* Informações principais */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <ClockIcon className="h-4 w-4 text-gray-400 dark:text-gray-500 flex-shrink-0" />
            <span className="text-sm text-gray-600 dark:text-gray-300">
              {formatDistanceToNow(new Date(lead.timestamp), {
                addSuffix: true,
                locale: ptBR,
              })}
            </span>
          </div>

          <div className="flex items-center gap-2">
            <LinkIcon className="h-4 w-4 text-gray-400 dark:text-gray-500 flex-shrink-0" />
            <span className="text-sm text-gray-600 dark:text-gray-300 truncate">
              {lead.campaignId}
            </span>
          </div>
        </div>

        {/* Tags */}
        <div className="mt-3 flex flex-wrap gap-2">
          {lead.utmSource && (
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
              {lead.utmSource}
            </span>
          )}
          {lead.utmContent && (
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
              {lead.utmContent}
            </span>
          )}
        </div>

        {/* Botão para mostrar localStorage */}
        {lead.localStorageData && (
          <button
            onClick={() => setShowLocalStorage(!showLocalStorage)}
            className="mt-3 w-full flex items-center justify-center gap-1 px-3 py-1.5 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors"
          >
            <DocumentTextIcon className="h-4 w-4" />
            {showLocalStorage ? (
              <>
                Ocultar Dados
                <ChevronUpIcon className="h-4 w-4" />
              </>
            ) : (
              <>
                Ver Dados
                <ChevronDownIcon className="h-4 w-4" />
              </>
            )}
          </button>
        )}

        {/* Dados do localStorage */}
        {showLocalStorage && lead.localStorageData && (
          <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-900/50 rounded-md">
            <pre className="text-xs text-gray-600 dark:text-gray-400 overflow-auto">
              {JSON.stringify(lead.localStorageData, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default LeadCard; 