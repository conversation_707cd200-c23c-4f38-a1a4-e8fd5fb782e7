import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // O token será obtido das variáveis de ambiente do servidor
    const token = process.env.CRM_TOKEN ;

    if (!token) {
      return NextResponse.json(
        { error: 'Token do CRM não configurado' },
        { status: 500 }
      );
    }

    return NextResponse.json({ token });
  } catch (error) {
    console.error('Erro ao obter token:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 