// src/components/CalendarHeader.tsx
import React from "react";
import { format, addMonths, subMonths } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Button } from "@/components/ui/button"; // Componente Button do ShadCN

interface CalendarHeaderProps {
  currentMonth: Date;
  setCurrentMonth: (date: Date) => void;
}

const CalendarHeader: React.FC<CalendarHeaderProps> = ({
  currentMonth,
  setCurrentMonth,
}) => {
  return (
    <div className="flex justify-between items-center mb-4 dark:shadow-gray-700 shadow-sm">
      <Button
        variant="outline"
        onClick={() => setCurrentMonth(subMonths(currentMonth, 1))}
        aria-label="Mês Anterior"
        className="p-2 rounded-md"
      >
        &lt;
      </Button>
      <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200">
        {format(currentMonth, "MMMM yyyy", { locale: ptBR })}
      </h2>
      <Button
        variant="outline"
        onClick={() => setCurrentMonth(addMonths(currentMonth, 1))}
        aria-label="Próximo Mês"
        className="p-2 rounded-md"
      >
        &gt;
      </Button>
    </div>
  );
};

export default CalendarHeader;
