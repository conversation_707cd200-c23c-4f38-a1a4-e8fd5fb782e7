import { useState, useEffect, useRef } from 'react';
import { create } from 'zustand';
import { getOpportunityDetails } from '@/services/crmService';
import { useRecoveryStore } from './useRecoveryStore';

interface OpportunityStageResponse {
  data: {
    stage_id: number;
    stage_name: string;
  };
}

interface StageStore {
  recoveryStates: Record<string, boolean>;
  setRecoveryState: (crmId: string, isInRecovery: boolean) => void;
  getRecoveryState: (crmId: string) => boolean;
}

const useStageStore = create<StageStore>((set, get) => ({
  recoveryStates: {},
  setRecoveryState: (crmId, isInRecovery) =>
    set((state) => ({
      recoveryStates: { ...state.recoveryStates, [crmId]: isInRecovery },
    })),
  getRecoveryState: (crmId) => get().recoveryStates[crmId] || false,
}));

const RECOVERY_STAGE_ID = 506789;

export const useOpportunityStage = (crmId: string, isModalOpen?: boolean) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const pollingInterval = useRef<NodeJS.Timeout>();
  const isProcessing = useRecoveryStore((state) => state.isProcessing(crmId));
  const initialCheckDone = useRef(false);
  const { setRecoveryState, getRecoveryState } = useStageStore();
  const isInRecovery = getRecoveryState(crmId);

  const checkOpportunityStage = async () => {
    try {
      const response = await getOpportunityDetails(crmId);
      const newIsInRecovery = response.data.stage_id === RECOVERY_STAGE_ID;
      
      if (newIsInRecovery !== isInRecovery) {
        setRecoveryState(crmId, newIsInRecovery);
      }
      
      setError(null);
    } catch (err) {
      console.error('Erro ao verificar estágio da oportunidade:', err);
      setError('Erro ao verificar estágio da oportunidade');
    } finally {
      setIsLoading(false);
    }
  };

  // Efeito para verificação inicial e quando o modal é aberto ou crmId mudar
  useEffect(() => {
    if (crmId) {
      initialCheckDone.current = true;
      checkOpportunityStage();
    }
  }, [crmId, isModalOpen]);

  // Efeito para polling quando o lead estiver sendo processado
  useEffect(() => {
    if (isProcessing && !pollingInterval.current) {
      // Inicia o polling a cada 2 segundos
      pollingInterval.current = setInterval(checkOpportunityStage, 2000);
    } else if (!isProcessing && pollingInterval.current) {
      // Para o polling quando não estiver mais processando
      clearInterval(pollingInterval.current);
      pollingInterval.current = undefined;
    }

    return () => {
      if (pollingInterval.current) {
        clearInterval(pollingInterval.current);
      }
    };
  }, [isProcessing, crmId]);

  return {
    isInRecovery,
    isLoading,
    error,
    refreshStage: checkOpportunityStage
  };
}; 