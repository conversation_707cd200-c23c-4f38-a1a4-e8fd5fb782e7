"use client";
import React, { useEffect } from 'react';
import useAuth from '@/context/useAuthStore';
import useMarketingStore from '@/context/useMarketingStore';
import MarketingStats from '@/components/marketing/MarketingStats';
import MarketingFilters from '@/components/marketing/MarketingFilters';
import LeadCard from '@/components/marketing/LeadCard';
import Spinner from '@/components/common/Spinner';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { FaFlask } from 'react-icons/fa';

const MarketingPage = () => {
  const token = useAuth((state) => state.token);
  const setShowLoginModal = useAuth((state) => state.setShowLoginModal);
  
  const {
    leads,
    isLoading,
    error,
    currentPage,
    totalPages,
    filters,
    setFilters,
    fetchLeads,
    exportToCSV,
    showTestLeads,
    setShowTestLeads,
  } = useMarketingStore();

  useEffect(() => {
    if (!token) {
      setShowLoginModal(true);
      return;
    }

    fetchLeads(token);
  }, [token, setShowLoginModal, fetchLeads, filters]);

  const handlePageChange = (newPage: number) => {
    setFilters({ page: newPage });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center dark:bg-gray-900">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center dark:bg-gray-900">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400 text-lg font-medium mb-2">Erro ao carregar dados</p>
          <p className="text-gray-600 dark:text-gray-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      <div className="container mx-auto px-4 max-w-7xl flex-1 flex flex-col overflow-hidden py-4">
        <div className="flex-none">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">
              Leads do Email Marketing
            </h1>
            <div className="flex items-center gap-4">
              {totalPages > 1 && (
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="inline-flex items-center gap-1 px-3 py-1.5 border border-gray-200 dark:border-gray-700 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                  >
                    <ChevronLeftIcon className="h-4 w-4" />
                    Anterior
                  </button>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {currentPage} / {totalPages}
                  </span>
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="inline-flex items-center gap-1 px-3 py-1.5 border border-gray-200 dark:border-gray-700 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                  >
                    Próxima
                    <ChevronRightIcon className="h-4 w-4" />
                  </button>
                </div>
              )}
              <button
                onClick={() => setShowTestLeads(!showTestLeads)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  showTestLeads 
                    ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300'
                    : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'
                } hover:bg-indigo-200 dark:hover:bg-indigo-800`}
              >
                <FaFlask className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {showTestLeads ? 'Ocultar Leads de Teste' : 'Mostrar Leads de Teste'}
                </span>
              </button>
            </div>
          </div>

          <MarketingStats leads={leads} />
          <MarketingFilters
            filters={filters}
            onFilterChange={setFilters}
            onExport={exportToCSV}
          />
        </div>

        <div className="flex-1 overflow-auto mt-4 -mx-4 px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 pb-4">
            {leads.map((lead) => (
              <LeadCard key={`${lead.timestamp}-${lead.ipAddress}`} lead={lead} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarketingPage; 