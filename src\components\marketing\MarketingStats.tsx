import React from 'react';
import { MarketingLead } from '@/types/marketing';
import { ChartBarIcon } from '@heroicons/react/24/outline';

interface MarketingStatsProps {
  leads: MarketingLead[];
}

const MarketingStats: React.FC<MarketingStatsProps> = ({ leads }) => {
  const campaignCounts = leads.reduce((acc, lead) => {
    acc[lead.campaignId] = (acc[lead.campaignId] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-3 mb-4">
      <div className="flex flex-wrap gap-3">
        {Object.entries(campaignCounts).map(([campaignId, count]) => (
          <div 
            key={campaignId}
            className="flex items-center gap-2 bg-gray-50 dark:bg-gray-700 rounded-md px-3 py-1.5 border border-gray-200 dark:border-gray-600"
          >
            <span className="text-sm text-gray-600 dark:text-gray-300 truncate max-w-[180px]">{campaignId}</span>
            <span className="text-sm font-semibold text-indigo-600 dark:text-indigo-400">{count}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MarketingStats; 