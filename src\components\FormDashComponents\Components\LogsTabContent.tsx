// components/FormDashComponents/LogsTabContent.tsx
import React, { useRef, useState } from "react";
import LogList from "@/components/FormDashComponents/LogList";
import { Opportunity } from "@/types/crm";

interface LogsTabContentProps {
  opportunities: Opportunity[];
}

const LogsTabContent: React.FC<LogsTabContentProps> = ({ opportunities }) => {
  const [selectedOpportunityId, setSelectedOpportunityId] = useState<number | null>(null);

  const logDetailsRef = useRef<HTMLDivElement>(null);
  const handleToggleLogs = (opportunityId: number, event: React.MouseEvent) => {
    if (
      logDetailsRef.current &&
      logDetailsRef.current.contains(event.target as Node)
    ) {
      return;
    }
    setSelectedOpportunityId((prev) =>
      prev === opportunityId ? null : opportunityId
    );
  };
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold mb-4">Detalhes dos Logs</h2>
      <ul className="mt-4 space-y-2">
        {opportunities.map((opp) => (
          <li
            key={opp.id}
            className="p-4 border rounded cursor-pointer transition-all bg-gray-50 border-gray-300 text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200"
            onClick={(e) => handleToggleLogs(opp.id, e)}
          >
            <p>
              <strong>ID Oportunidade:</strong>{" "}
              <a
                href={`https://app.pipe.run/pipeline/gerenciador/visualizar/${opp.crmId}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 hover:text-blue-700 dark:hover:text-blue-300"
              >
                {opp.crmId}
              </a>
            </p>
            <p>
              <strong>Status:</strong>{" "}
              {opp.isCompleted ? "Completo" : "Em Progresso"}
            </p>
            {selectedOpportunityId === opp.id && (
              <div className="mt-2">
                <strong>Logs:</strong>
                <LogList logs={opp.Log || []} />
              </div>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default LogsTabContent;
