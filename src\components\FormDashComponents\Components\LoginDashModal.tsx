/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */

"use client";

import React, { useState } from "react";
import axios from "axios";
import Image from "next/image";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import useAuthStore from "@/context/useAuthStore";
interface LoginDashModalProps {
  onSuccess?: (token: string) => void;
}
const LoginDashModal: React.FC<LoginDashModalProps> = () => {
  const [email, setEmail] = useState("");
  const [senha, setSenha] = useState("");
  const [token2FA, setToken2FA] = useState("");
  const [error, setError] = useState("");
  const [stage, setStage] = useState<"login" | "2fa">("login");
  const [loading, setLoading] = useState(false);

  const login = useAuthStore((state) => state.login);
  const setShowLoginModal = useAuthStore((state) => state.setShowLoginModal);

  // Primeiro passo: Verificação da senha
  const handleLogin = async () => {
    setError("");
    if (!email || !senha) {
      setError("Por favor, preencha todos os campos.");
      return;
    }

    setLoading(true);
    try {
      const response = await axios.post(
        "https://crm-api.registrese.app.br/auth/login",
        {
          email,
          pass: senha,
        }
      );
      toast.success(response.data.message, {
        autoClose: 900,
      });

      setStage("2fa"); // Move para a próxima etapa (inserção do token 2FA)
    } catch (err: any) {
      console.error("Erro ao fazer login:", err);
      const message =
        err.response?.data?.message ||
        "Falha na autenticação. Verifique suas credenciais.";
      toast.error(message, {
        autoClose: 900,
      });
      setError(message);
    } finally {
      setLoading(false);
    }
  };

  // Segundo passo: Verificação do token 2FA
  const handleVerify2FA = async () => {
    setError("");
    if (!token2FA) {
      setError("Por favor, insira o token 2FA.");
      return;
    }

    setLoading(true);
    try {
      const response = await axios.post(
        "https://crm-api.registrese.app.br/auth/verify-2fa",
        {
          email,
          twoFactorToken: token2FA,
        }
      );
      toast.success(response.data.message, {
        autoClose: 900,
      });

      const jwtToken = response.data.token;
      login(jwtToken); // Atualiza o estado de autenticação no store
    } catch (err: any) {
      console.error("Erro na verificação 2FA:", err);
      const message = err.response?.data?.message || "Token 2FA inválido.";
      toast.error(message, {
        autoClose: 900,
      });
      setError(message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-300 bg-opacity-75 z-50">
      <div className="bg-white dark:bg-gray-800 p-8 rounded shadow-lg flex flex-col items-center gap-4 w-80">
        <Image
          src="/logo.svg" // Certifique-se de que o caminho está correto
          alt="Registre.se"
          className="w-auto h-10 object-cover mb-4"
          width={150}
          height={50}
          priority
        />

        {stage === "login" ? (
          <>
            <input
              type="email"
              placeholder="Email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleLogin();
                }
              }}
              className="mb-2 p-2 border border-gray-300 rounded w-full outline-none focus:outline-verde focus:border-none text-gray-950 dark:text-gray-600"
            />
            <input
              type="password"
              placeholder="Senha"
              value={senha}
              onChange={(e) => setSenha(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleLogin();
                }
              }}
              className="mb-2 p-2 border border-gray-300 rounded w-full outline-none focus:outline-verde focus:border-none text-gray-950 dark:text-gray-600"
            />
            <button
              onClick={handleLogin}
              className="bg-verdeDark w-full text-white px-4 py-2 rounded hover:bg-verde hover:scale-105 transition duration-100 ease-in disabled:opacity-50"
              disabled={loading}
            >
              {loading ? "Acessando..." : "Acessar"}
            </button>
          </>
        ) : (
          <>
            <input
              type="text"
              placeholder="Token 2FA"
              value={token2FA}
              onChange={(e) => setToken2FA(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleVerify2FA();
                }
              }}
              className="mb-2 p-2 border border-gray-300 rounded w-full outline-none focus:outline-verde focus:border-none text-gray-950 dark:text-gray-600"
            />
            <button
              onClick={handleVerify2FA}
              className="bg-verde w-full text-white px-4 py-2 rounded hover:bg-verde-dark transition duration-100 ease-in disabled:opacity-50"
              disabled={loading}
            >
              {loading ? "Verificando..." : "Verificar Token 2FA"}
            </button>
          </>
        )}

        {error && <p className="text-red-500">{error}</p>}
      </div>
    </div>
  );
};

export default LoginDashModal;
