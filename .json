[{"crmId": "43274995", "formData": {"brandTitle": "Ra jeans", "leadName": "<PERSON> ", "leadNumber": "(34) 99644-9009", "leadMail": "<EMAIL>", "businessInternship": "<PERSON><PERSON> atuo há mais de 2 anos", "businessCollaborators": "1 a 2", "haveCNPJ": "Sim.", "taxFramework": "ME", "businessDefinition": "Vestu<PERSON><PERSON>", "clothingActing": ["Revendo peças de outras marcas"], "clientSiteOrSocialLink": "<PERSON>m", "utmSource": ["Google"], "utmCampaign": "[\"GDM\"]", "firstTimeUrl": "https://registre.se/?utm_source=Google&utm_campaign=GDM&utm_content=Imagem&gad_source=1&gclid=CjwKCAiAmMC6BhA6EiwAdN5iLWaZgrqPQd0uxKiCDCPqtdyvLvh7UEcjEvAkqU4HY43n1QsecbvgExoCsecQAvD_BwE", "score": 160, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-04T13:40:07-03:00"}, "createdAt": "2024-12-04T16:40:42.000Z", "Log": [{"timestamp": "2024-12-04T16:40:42.000Z"}, {"timestamp": "2024-12-04T16:40:50.000Z"}, {"timestamp": "2024-12-04T16:40:53.000Z"}, {"timestamp": "2024-12-04T16:40:56.000Z"}, {"timestamp": "2024-12-04T16:40:58.000Z"}, {"timestamp": "2024-12-04T16:41:02.000Z"}, {"timestamp": "2024-12-04T16:41:15.000Z"}, {"timestamp": "2024-12-04T16:41:18.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43277892", "formData": {"brandTitle": "Permitasestudio ", "leadName": "<PERSON><PERSON><PERSON>", "leadNumber": "(47) 99962-2952", "leadMail": "<EMAIL>", "utmSource": ["Google"], "utmCampaign": "[\"BFROAS\"]", "firstTimeUrl": "https://www.registre.se/?utm_source=Google&utm_campaign=BFROAS&utm_content=F&utm_term=patentear%20marca&device=m&gad_source=1&gbraid=0AAAAAohu6SSOWp2FAYCTWV7Gifoxi1oev&gclid=CjwKCAiAmMC6BhA6EiwAdN5iLQ6YlWw-rJuTrqRhAxVPIr8soO8HXmUAumYi487_sEsw84LqM6inTRoCIdcQAvD_BwE", "utmFinal": null, "campaignHistory": null, "score": 60, "firstVisitTime": "2024-12-04T15:16:47-03:00"}, "createdAt": "2024-12-04T18:17:56.000Z", "Log": [{"timestamp": "2024-12-04T18:17:56.000Z"}], "inRecovery": false, "stageName": "Entrada - Form Incompleto"}, {"crmId": "43277123", "formData": {"brandTitle": "shape 90", "leadName": "rafael", "leadNumber": "(61) 99232-7235", "leadMail": "<EMAIL>", "utmSource": ["Google"], "utmCampaign": "[\"BFROAS\"]", "firstTimeUrl": "https://www.registre.se/?utm_source=Google&utm_campaign=BFROAS&utm_content=F&utm_term=registrar%20inpi&device=c&gad_source=1&gclid=CjwKCAiAmMC6BhA6EiwAdN5iLcnZmPg_d--gWBsPIYe0ST_cUWx4CEdk5Cvj4jzrJj5_rReX0kYWdBoC31UQAvD_BwE", "utmFinal": null, "campaignHistory": null, "score": 60, "firstVisitTime": "2024-12-04T14:53:01-03:00"}, "createdAt": "2024-12-04T17:53:48.000Z", "Log": [{"timestamp": "2024-12-04T17:53:48.000Z"}], "inRecovery": false, "stageName": "Entrada - Form Incompleto"}, {"crmId": "43277131", "formData": {"brandTitle": "Grupo guidare", "leadName": "<PERSON> ", "leadNumber": "(62) 99258-6837", "leadMail": "<EMAIL>", "businessInternship": "<PERSON><PERSON> atuo há mais de 2 anos", "businessCollaborators": "3 a 5", "haveCNPJ": "Sim.", "taxFramework": "<PERSON><PERSON> sei responder.", "businessDefinition": "Prestação de serviço", "businessDefinitionDescription": "Recebimento de indenização ", "clientSiteOrSocialLink": "<PERSON>m", "utmSource": ["Google"], "utmCampaign": "[\"Tagg_Listas\"]", "firstTimeUrl": "https://registre.se/?utm_campaign=Tagg_Listas&utm_source=Google&utm_medium=Pesquisa&utm_content=GA_DSA&utm_keyword=&utm_adID=************&utm_targetid=dsa-***********&utm_matchtype=&utm_local=20106&utm_device=m&utm_network=g&gad_source=1&gbraid=0AAAAAohu6SRUxfrkAWCzoEolFktthyUIa&gclid=CjwKCAiAmMC6BhA6EiwAdN5iLQoTMmvvMKB_SP-E1Rqyq3i8f0xvluOw8ZDQFxHK1Yy9K5x8Cz5XfBoCYzQQAvD_BwE", "score": 150, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-04T14:53:12-03:00"}, "createdAt": "2024-12-04T17:54:04.000Z", "Log": [{"timestamp": "2024-12-04T17:54:04.000Z"}, {"timestamp": "2024-12-04T17:54:10.000Z"}, {"timestamp": "2024-12-04T17:54:14.000Z"}, {"timestamp": "2024-12-04T17:54:27.000Z"}, {"timestamp": "2024-12-04T17:54:31.000Z"}, {"timestamp": "2024-12-04T17:54:35.000Z"}, {"timestamp": "2024-12-04T17:54:44.000Z"}, {"timestamp": "2024-12-04T17:54:47.000Z"}], "inRecovery": false, "stageName": "Entrada - Form Incompleto"}, {"crmId": "43277212", "formData": {"brandTitle": "Playfutsal", "leadName": "<PERSON>", "leadNumber": "(11) 99847-4541", "leadMail": "<EMAIL>", "businessInternship": "<PERSON><PERSON> atuo há mais de 2 anos", "businessCollaborators": "1 a 2", "haveCNPJ": "Sim.", "taxFramework": "MEI", "businessDefinition": "Prestação de serviço", "utmSource": ["Google"], "utmCampaign": "[\"BFROAS\"]", "firstTimeUrl": "https://www.registre.se/?utm_source=Google&utm_campaign=BFROAS&utm_content=F&utm_term=patentear%20marca&device=m&gad_source=1&gbraid=0AAAAAohu6SSOWp2FAYCTWV7Gifoxi1oev", "score": 145, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-04T14:55:59-03:00"}, "createdAt": "2024-12-04T17:56:51.000Z", "Log": [{"timestamp": "2024-12-04T17:56:51.000Z"}, {"timestamp": "2024-12-04T17:57:00.000Z"}, {"timestamp": "2024-12-04T17:57:06.000Z"}, {"timestamp": "2024-12-04T17:57:08.000Z"}, {"timestamp": "2024-12-04T17:57:10.000Z"}, {"timestamp": "2024-12-04T17:57:14.000Z"}], "inRecovery": false, "stageName": "Entrada - Form Incompleto"}, {"crmId": "43264488", "formData": {"brandTitle": "Experiência bim", "leadName": "<PERSON><PERSON> ", "leadNumber": "(11) 99700-5257", "leadMail": "<EMAIL>", "businessInternship": "Por enquanto é só um plano", "consultationPurpose": "Somente saber se está disponível.", "businessCollaborators": "3 a 5", "haveCNPJ": "Atuo como pessoa física", "businessDefinition": "Prestação de serviço", "utmSource": "", "utmCampaign": null, "firstTimeUrl": "https://registre.se/?gad_source=1&gbraid=0AAAAAohu6SQljF9Zr5Ua2jpqysNTRATTT&gclid=CjwKCAiAmMC6BhA6EiwAdN5iLSfQuht6JFwE6teUJ3BbRxkDKsuYEzIWUkUgWmtRp8xpXpbQ4iouVhoCcJ4QAvD_BwE", "score": 61, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-04T08:03:12-03:00"}, "createdAt": "2024-12-04T11:03:59.000Z", "Log": [{"timestamp": "2024-12-04T11:03:59.000Z"}, {"timestamp": "2024-12-04T11:04:04.000Z"}, {"timestamp": "2024-12-04T11:04:07.000Z"}, {"timestamp": "2024-12-04T11:04:11.000Z"}, {"timestamp": "2024-12-04T11:04:28.000Z"}, {"timestamp": "2024-12-04T11:04:34.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43280514", "formData": {"brandTitle": "SISO", "leadName": "kkk", "leadNumber": "(99) 99999-9999", "leadMail": "<EMAIL>", "utmSource": "", "utmCampaign": "[\"AMPS\"]", "firstTimeUrl": "https://www.registre.se/?https://www.registre.se/?utm_source=Google&utm_campaign=AMPS&gad_source=1&gclid=EAIaIQobChMI-rTgsuuOigMV6V9IAB1szSEREAAYASAAEgI-XPD_BwE", "utmFinal": null, "campaignHistory": null, "score": 40, "firstVisitTime": "2024-12-04T08:07:02-03:00"}, "createdAt": "2024-12-04T19:24:48.000Z", "Log": [{"timestamp": "2024-12-04T19:24:48.000Z"}], "inRecovery": false, "stageName": "Entrada - Form Incompleto"}, {"crmId": "43273975", "formData": {"brandTitle": "Computer remote", "leadName": "<PERSON> ", "leadNumber": "(81) 98552-5200", "leadMail": "<EMAIL>", "businessInternship": "Por enquanto é só um plano", "consultationPurpose": "Se estiver disponível, quero registrar.", "businessCollaborators": "1 a 2", "haveCNPJ": "Atuo como pessoa física", "businessDefinition": "Software e/ou tecnologia", "businessDefinitionDescription": "Advogado ", "clientSiteOrSocialLink": "<PERSON>m", "utmSource": ["Google"], "utmCampaign": "[\"GDM\"]", "firstTimeUrl": "https://registre.se/?utm_source=Google&utm_campaign=GDM&utm_content=Imagem&gad_source=1&gclid=CjwKCAiAmMC6BhA6EiwAdN5iLemu7PL7wFlV0YMrXAVLf19adMdhiu0BfTzX1bzXggt2C7LAtvwNfBoC7Q8QAvD_BwE", "score": 86, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-04T12:47:37-03:00"}, "createdAt": "2024-12-04T15:48:50.000Z", "Log": [{"timestamp": "2024-12-04T15:48:51.000Z"}, {"timestamp": "2024-12-04T15:48:59.000Z"}, {"timestamp": "2024-12-04T15:49:06.000Z"}, {"timestamp": "2024-12-04T15:49:10.000Z"}, {"timestamp": "2024-12-04T15:49:26.000Z"}, {"timestamp": "2024-12-04T15:49:30.000Z"}, {"timestamp": "2024-12-04T15:49:39.000Z"}, {"timestamp": "2024-12-04T15:49:42.000Z"}], "inRecovery": false, "stageName": "Entrada - Form Incompleto"}, {"crmId": "43251687", "formData": {"brandTitle": "<PERSON><PERSON><PERSON> nerd", "leadName": "<PERSON><PERSON><PERSON>", "leadNumber": "(14) 99601-6602", "leadMail": "<EMAIL>", "utmSource": ["Google "], "utmCampaign": "[\"PMBRMK\"]", "firstTimeUrl": "https://registre.se/?utm_source=Google+&utm_campaign=PMBRMK&gad_source=1&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoOBwlodkIKz-_43Ma3Izl_4-L4Ijp3WOBRstiC64vKN95mrCyEOerhoC1N4QAvD_BwE", "utmFinal": null, "campaignHistory": null, "score": 40, "firstVisitTime": "2024-12-03T15:19:58-03:00"}, "createdAt": "2024-12-03T18:20:41.000Z", "Log": [{"timestamp": "2024-12-03T18:20:42.000Z"}], "inRecovery": false, "stageName": "Entrada - Form Incompleto"}, {"crmId": "43251774", "formData": {"brandTitle": "<PERSON>", "leadName": "<PERSON><PERSON>", "leadNumber": "(31) 99681-7719", "leadMail": "<EMAIL>", "businessInternship": "Comecei recentemente", "businessCollaborators": "3 a 5", "haveCNPJ": "Não, mas estamos providenciando.", "notCnpjTaxFramework": "MEI", "businessDefinition": "Prestação de serviço", "utmSource": ["Google "], "utmCampaign": "[\"PMBRMK\"]", "firstTimeUrl": "https://registre.se/?utm_source=Google+&utm_campaign=PMBRMK&gad_source=1&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoD02B6p6LGgCas-JUmqRRvhctP1Dmn-b1k1XJh3_yCtGXcNEYNaZthoCLLsQAvD_BwE", "score": 108, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-03T15:20:09-03:00"}, "createdAt": "2024-12-03T18:20:54.000Z", "Log": [{"timestamp": "2024-12-03T18:20:54.000Z"}, {"timestamp": "2024-12-03T18:20:59.000Z"}, {"timestamp": "2024-12-03T18:21:03.000Z"}, {"timestamp": "2024-12-03T18:21:07.000Z"}, {"timestamp": "2024-12-03T18:21:13.000Z"}, {"timestamp": "2024-12-03T18:21:19.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43247057", "formData": {"brandTitle": "Pedro Gráfica Express", "leadName": "<PERSON> ", "leadNumber": "(27) 99252-9344", "leadMail": "<EMAIL>", "businessInternship": "Comecei recentemente", "businessCollaborators": "1 a 2", "haveCNPJ": "Não, mas estamos providenciando.", "notCnpjTaxFramework": "<PERSON><PERSON> sei responder", "businessDefinition": "Prestação de serviço", "utmSource": "", "utmCampaign": "[\"AMPS\"]", "firstTimeUrl": "https://www.registre.se/?https://www.registre.se/?utm_source=Google&utm_campaign=AMPS&gad_source=1&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoJRTzQvQuliHr7wihQMxAa6GhdNmQHvwIiDxFk10RzKtU36RSh7HxhoCsp8QAvD_BwE", "score": 103, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-03T12:45:36-03:00"}, "createdAt": "2024-12-03T15:46:07.000Z", "Log": [{"timestamp": "2024-12-03T15:46:07.000Z"}, {"timestamp": "2024-12-03T15:46:17.000Z"}, {"timestamp": "2024-12-03T15:46:22.000Z"}, {"timestamp": "2024-12-03T15:46:26.000Z"}, {"timestamp": "2024-12-03T15:46:32.000Z"}, {"timestamp": "2024-12-03T15:46:41.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43248795", "formData": {"brandTitle": "2r metal<PERSON><PERSON>", "leadName": "graziele", "leadNumber": "(51) 99295-0858", "leadMail": "<EMAIL>", "utmSource": ["Google"], "utmCampaign": "[\"BFEMP\"]", "firstTimeUrl": "https://www.registre.se/?utm_source=Google&utm_campaign=BFEMP&utm_content=F&utm_term=registro%20inpi&device=c&gad_source=1&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoKVzLG148evPf22AJTyPoxxM_Pq9iDaaTJK45F05nYfOBKPlwsB4XRoCFkwQAvD_BwE", "utmFinal": null, "campaignHistory": null, "score": 60, "firstVisitTime": "2024-12-03T14:00:03-03:00"}, "createdAt": "2024-12-03T17:00:43.000Z", "Log": [{"timestamp": "2024-12-03T17:00:43.000Z"}], "inRecovery": false, "stageName": "Entrada - Form Incompleto"}, {"crmId": "43248152", "formData": {"brandTitle": "Mequecoxinha", "leadName": "<PERSON><PERSON><PERSON><PERSON> ", "leadNumber": "(11) 97395-9577", "leadMail": "<EMAIL>", "utmSource": ["Google "], "utmCampaign": "[\"PMBRMK\"]", "firstTimeUrl": "https://registre.se/?utm_source=Google+&utm_campaign=PMBRMK&gad_source=1&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoB6luqD9fpkg0u00LoGORwpOJTV3SOMVGV_mMw6Pi9h6btcdkYSexRoCh8UQAvD_BwE", "utmFinal": null, "campaignHistory": null, "score": 40, "firstVisitTime": "2024-12-03T13:35:05-03:00"}, "createdAt": "2024-12-03T16:35:53.000Z", "Log": [{"timestamp": "2024-12-03T16:35:53.000Z"}], "inRecovery": false, "stageName": "Entrada - Form Incompleto"}, {"crmId": "43247451", "formData": {"brandTitle": "Crazy About Açaí ", "leadName": "<PERSON><PERSON><PERSON> ", "leadNumber": "(51) 99448-4999", "leadMail": "<EMAIL>", "businessInternship": "<PERSON><PERSON> atuo há mais de 6 meses", "businessCollaborators": "1 a 2", "haveCNPJ": "Sim.", "taxFramework": "MEI", "businessDefinition": "Outros", "businessDefinitionDescription": "Acai", "clientSiteOrSocialLink": "<PERSON>m", "utmSource": ["Google "], "utmCampaign": "[\"PMBRMK\"]", "firstTimeUrl": "https://registre.se/?utm_source=Google+&utm_campaign=PMBRMK&gad_source=1&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoP4sDAePsPYhcUgRTlaOUO9QaQM-lRpWH1KwkfI4xR2-mzSBKXohLBoCI4UQAvD_BwE", "score": 125, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-03T13:04:14-03:00"}, "createdAt": "2024-12-03T16:04:57.000Z", "Log": [{"timestamp": "2024-12-03T16:05:07.000Z"}, {"timestamp": "2024-12-03T16:04:57.000Z"}, {"timestamp": "2024-12-03T16:05:04.000Z"}, {"timestamp": "2024-12-03T16:05:09.000Z"}, {"timestamp": "2024-12-03T16:05:11.000Z"}, {"timestamp": "2024-12-03T16:05:19.000Z"}, {"timestamp": "2024-12-03T16:05:24.000Z"}, {"timestamp": "2024-12-03T16:05:27.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43235910", "formData": {"brandTitle": "uselluze", "leadName": "ricao", "leadNumber": "(19) 45454-5874", "leadMail": "<EMAIL>", "utmSource": ["Google "], "utmCampaign": "[\"PMBRMK\"]", "firstTimeUrl": "https://registre.se/?utm_source=Google+&utm_campaign=PMBRMK&gad_source=1&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoDtzVWohDTYKPao9-dl8TO88vCVOSGYz6ZxM-Wd80L0hJc4YJkA5whoCZaYQAvD_BwE", "utmFinal": null, "campaignHistory": null, "score": 40, "firstVisitTime": "2024-12-03T09:31:27-03:00"}, "createdAt": "2024-12-03T12:32:01.000Z", "Log": [{"timestamp": "2024-12-03T12:32:01.000Z"}], "inRecovery": false, "stageName": "Entrada - Form Incompleto"}, {"crmId": "43235883", "formData": {"brandTitle": "Moose Coast", "leadName": "Victor ", "leadNumber": "(22) 99732-6389", "leadMail": "<EMAIL>", "businessInternship": "Comecei recentemente", "businessCollaborators": "1 a 2", "haveCNPJ": "Atuo como pessoa física", "businessDefinition": "Vestu<PERSON><PERSON>", "clothingActing": ["Tenho peças de marca própria", "<PERSON><PERSON><PERSON>"], "clientSiteOrSocialLink": "<PERSON>m", "utmSource": ["Google"], "utmCampaign": "[\"BFROAS\"]", "firstTimeUrl": "https://www.registre.se/?utm_source=Google&utm_campaign=BFROAS&utm_content=F&utm_term=registrar%20marca&device=m&gad_source=1&gbraid=0AAAAAohu6SRlqkqqy2OjFOaQYVU4lbF6U&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoJj5jTj-QoxVn68PhyKBvt3-T3zAsg_nkDJriq6icDMdTnX1Zr-2lxoCtisQAvD_BwE", "score": 121, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-03T09:29:18-03:00"}, "createdAt": "2024-12-03T12:30:55.000Z", "Log": [{"timestamp": "2024-12-03T12:30:55.000Z"}, {"timestamp": "2024-12-03T12:31:13.000Z"}, {"timestamp": "2024-12-03T12:31:18.000Z"}, {"timestamp": "2024-12-03T12:31:28.000Z"}, {"timestamp": "2024-12-03T12:31:34.000Z"}, {"timestamp": "2024-12-03T12:31:50.000Z"}, {"timestamp": "2024-12-03T12:31:54.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43236109", "formData": {"brandTitle": "BR1020150214359.", "leadName": "LEONARDO ORLANDO", "leadNumber": "(54) 99243-7861", "leadMail": "<EMAIL>", "utmSource": ["Google"], "utmCampaign": "[\"BFEMP\"]", "firstTimeUrl": "https://www.registre.se/?utm_source=Google&utm_campaign=BFEMP&utm_content=F&utm_term=registro%20inpi&device=c&gad_source=1&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoDBpx0GHKma40a6ZzYdegaLUx0s8bCw-Fh0ZLBQ9Mqok-IXi8-ePWRoCq5gQAvD_BwE", "utmFinal": null, "campaignHistory": null, "score": 60, "firstVisitTime": "2024-12-03T09:33:43-03:00"}, "createdAt": "2024-12-03T12:34:29.000Z", "Log": [{"timestamp": "2024-12-03T12:34:29.000Z"}], "inRecovery": false, "stageName": "Entrada - Form Incompleto"}, {"crmId": "43238599", "formData": {"brandTitle": "<PERSON><PERSON>", "leadName": "Giovanna", "leadNumber": "(51) 98658-0863", "leadMail": "<EMAIL>", "businessInternship": "Pretendo começar em breve", "consultationPurpose": "Confirmar se o nome está disponível, antes de criar o logotipo.", "businessCollaborators": "11 a 50", "haveCNPJ": "Atuo como pessoa física", "businessDefinition": "Fabricação de produtos", "businessDefinitionDescription": "velas", "clientSiteOrSocialLink": "<PERSON>m", "utmSource": ["Google "], "utmCampaign": "[\"PMBRMK\"]", "firstTimeUrl": "https://registre.se/?utm_source=Google+&utm_campaign=PMBRMK&gad_source=1&gbraid=0AAAAAohu6SR-AHOEVPrRwZCVa0njCvyUY&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoDVqxfTXgmALmBCGOA3MdpH7X-YUcCuaw7gtv760fIuxgkshreYjGBoC-jgQAvD_BwE", "score": 108, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-03T10:09:21-03:00"}, "createdAt": "2024-12-03T13:09:55.000Z", "Log": [{"timestamp": "2024-12-03T13:09:56.000Z"}, {"timestamp": "2024-12-03T13:10:03.000Z"}, {"timestamp": "2024-12-03T13:10:24.000Z"}, {"timestamp": "2024-12-03T13:10:28.000Z"}, {"timestamp": "2024-12-03T13:10:32.000Z"}, {"timestamp": "2024-12-03T13:10:38.000Z"}, {"timestamp": "2024-12-03T13:10:43.000Z"}, {"timestamp": "2024-12-03T13:10:48.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43238630", "formData": {"brandTitle": "softacademy", "leadName": "<PERSON>", "leadNumber": "(47) 99931-3151", "leadMail": "<EMAIL>", "businessInternship": "Comecei recentemente", "businessCollaborators": "1 a 2", "haveCNPJ": "Atuo como pessoa física", "businessDefinition": "Software e/ou tecnologia", "utmSource": ["Google"], "utmCampaign": "[\"BGBC\"]", "firstTimeUrl": "https://registre.se/?utm_source=Google&utm_campaign=BGBC&utm_content=BCLEAD&utm_term=registrar%20marca&device=c&gad_source=1&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoAq6-XI4IVB9itcPB9s17VFuGwG8v4sWbLxVo0dGnfIGntYYt_tGgRoChhYQAvD_BwE", "score": 121, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-03T10:09:42-03:00"}, "createdAt": "2024-12-03T13:11:08.000Z", "Log": [{"timestamp": "2024-12-03T13:11:08.000Z"}, {"timestamp": "2024-12-03T13:11:26.000Z"}, {"timestamp": "2024-12-03T13:11:30.000Z"}, {"timestamp": "2024-12-03T13:11:41.000Z"}, {"timestamp": "2024-12-03T13:11:52.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43238754", "formData": {"brandTitle": "Barbearia imperio do homem ", "leadName": "<PERSON>", "leadNumber": "(65) 99987-3883", "leadMail": "rafael10<PERSON><EMAIL>", "businessInternship": "Por enquanto é só um plano", "consultationPurpose": "Somente saber se está disponível.", "utmSource": "", "utmCampaign": "[\"AMPS\"]", "firstTimeUrl": "https://www.registre.se/?https://www.registre.se/?utm_source=Google&utm_campaign=AMPS&gad_source=1&gbraid=0AAAAAohu6SSlMTPBA8l25EVjBIufOk-ft&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoAXecJnKXrm9L-fqAnEll3AIjyn3RSV15Hdu7lgjctGTq5xzwOht4BoCuT4QAvD_BwE", "score": 40, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-03T10:14:36-03:00"}, "createdAt": "2024-12-03T13:15:24.000Z", "Log": [{"timestamp": "2024-12-03T13:15:24.000Z"}, {"timestamp": "2024-12-03T13:15:34.000Z"}, {"timestamp": "2024-12-03T13:15:37.000Z"}], "inRecovery": false, "stageName": "Entrada - Form Incompleto"}, {"crmId": "43238982", "formData": {"brandTitle": "011 Drones", "leadName": "<PERSON>", "leadNumber": "(11) 92013-1621", "leadMail": "<EMAIL>", "businessInternship": "Comecei recentemente", "businessCollaborators": "1 a 2", "haveCNPJ": "Não, mas estamos providenciando.", "utmSource": ["Google "], "utmCampaign": "[\"PMBRMK\"]", "firstTimeUrl": "https://registre.se/?utm_source=Google+&utm_campaign=PMBRMK&gad_source=1&gbraid=0AAAAAohu6SR-AHOEVPrRwZCVa0njCvyUY&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoPNWo4xiquv8kA3b1XCCOxv04wtsdwqKQzMmsCiJKyzyPv2CPhiiIxoCU8MQAvD_BwE", "score": 103, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-03T10:21:22-03:00"}, "createdAt": "2024-12-03T13:22:01.000Z", "Log": [{"timestamp": "2024-12-03T13:22:02.000Z"}, {"timestamp": "2024-12-03T13:22:07.000Z"}, {"timestamp": "2024-12-03T13:22:10.000Z"}, {"timestamp": "2024-12-03T13:22:13.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43239054", "formData": {"brandTitle": "RetinAI", "leadName": "<PERSON>", "leadNumber": "(16) 98196-2163", "leadMail": "he<PERSON><PERSON><PERSON>@gmail.com", "businessInternship": "<PERSON><PERSON> atuo há mais de 6 meses", "businessCollaborators": "1 a 2", "haveCNPJ": "Sim.", "taxFramework": "ME", "businessDefinition": "Software e/ou tecnologia", "utmSource": "", "utmCampaign": null, "firstTimeUrl": "https://registre.se/", "score": 140, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-09-28T10:37:47-03:00"}, "createdAt": "2024-12-03T13:23:30.000Z", "Log": [{"timestamp": "2024-12-03T13:23:31.000Z"}, {"timestamp": "2024-12-03T13:23:37.000Z"}, {"timestamp": "2024-12-03T13:23:40.000Z"}, {"timestamp": "2024-12-03T13:23:47.000Z"}, {"timestamp": "2024-12-03T13:23:58.000Z"}, {"timestamp": "2024-12-03T13:24:02.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43239809", "formData": {"brandTitle": "Acafro ", "leadName": "<PERSON><PERSON> ", "leadNumber": "(11) 99493-8043", "leadMail": "<EMAIL>", "businessInternship": "<PERSON><PERSON> atuo há mais de 2 anos", "businessCollaborators": "11 a 50", "haveCNPJ": "Sim.", "taxFramework": "DEMAIS", "businessDefinition": "Outros", "businessDefinitionDescription": "Associação ", "clientSiteOrSocialLink": "<PERSON>m", "utmSource": ["Google"], "utmCampaign": "[\"GDM\"]", "firstTimeUrl": "https://registre.se/?utm_source=Google&utm_campaign=GDM&utm_content=Imagem&gad_source=1&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoLzw-wtLH-77htUvMPJAJ4o_vBwM1gFkVsLI3JIQWFu-XNcPUUL1ZxoCRIEQAvD_BwE", "score": 222, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-03T10:45:32-03:00"}, "createdAt": "2024-12-03T13:46:24.000Z", "Log": [{"timestamp": "2024-12-03T13:46:24.000Z"}, {"timestamp": "2024-12-03T13:46:36.000Z"}, {"timestamp": "2024-12-03T13:46:51.000Z"}, {"timestamp": "2024-12-03T13:46:54.000Z"}, {"timestamp": "2024-12-03T13:47:02.000Z"}, {"timestamp": "2024-12-03T13:47:08.000Z"}, {"timestamp": "2024-12-03T13:47:14.000Z"}, {"timestamp": "2024-12-03T13:47:19.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43248040", "formData": {"brandTitle": "whoami", "leadName": "<PERSON>", "leadNumber": "(11) 95347-2529", "leadMail": "<EMAIL>", "businessInternship": "Por enquanto é só um plano", "consultationPurpose": "Somente saber se está disponível.", "businessCollaborators": "1 a 2", "haveCNPJ": "Não, mas estamos providenciando.", "notCnpjTaxFramework": "MEI", "businessDefinition": "Software e/ou tecnologia", "utmSource": ["Google"], "utmCampaign": "[\"Tagg_Listas\"]", "firstTimeUrl": "https://www.registre.se/?utm_campaign=Tagg_Listas&utm_source=Google&utm_medium=Pesquisa&utm_content=GA_DSA&utm_keyword=&utm_adID=************&utm_targetid=dsa-***********&utm_matchtype=&utm_local=1001773&utm_device=m&utm_network=g&gad_source=1&gbraid=0AAAAAohu6SQOpq18OmPTQ80rf75bBoNQn", "score": 78, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-03T13:30:28-03:00"}, "createdAt": "2024-12-03T16:31:00.000Z", "Log": [{"timestamp": "2024-12-03T16:31:00.000Z"}, {"timestamp": "2024-12-03T16:31:04.000Z"}, {"timestamp": "2024-12-03T16:31:07.000Z"}, {"timestamp": "2024-12-03T16:31:12.000Z"}, {"timestamp": "2024-12-03T16:31:16.000Z"}, {"timestamp": "2024-12-03T16:31:21.000Z"}, {"timestamp": "2024-12-03T16:31:23.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43253451", "formData": {"brandTitle": "pazinato tv ltda", "leadName": "alex pazinato de <PERSON>", "leadNumber": "(34) 53453-4534", "leadMail": "<EMAIL>", "utmSource": "", "utmCampaign": null, "firstTimeUrl": "https://registre.se/?gad_source=1&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoPa_2NnH9l-HgekLLAP8OWd7JWKdIWkU0PwYnhFCP48oCy1sr7ceWBoC2KgQAvD_BwE", "utmFinal": null, "campaignHistory": null, "score": 40, "firstVisitTime": "2024-12-03T16:11:24-03:00"}, "createdAt": "2024-12-03T19:11:47.000Z", "Log": [{"timestamp": "2024-12-03T19:11:47.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43254505", "formData": {"brandTitle": "Flores & Borboletas ", "leadName": "<PERSON><PERSON><PERSON> ", "leadNumber": "(62) 98463-7331", "leadMail": "<EMAIL>", "businessInternship": "Comecei recentemente", "businessCollaborators": "1 a 2", "haveCNPJ": "Atuo como pessoa física", "businessDefinition": "Vestu<PERSON><PERSON>", "clothingActing": ["Tenho peças de marca própria"], "clientSiteOrSocialLink": "<PERSON>m", "utmSource": ["Google"], "utmCampaign": "[\"BFEMP\"]", "firstTimeUrl": "https://www.registre.se/?utm_source=Google&utm_campaign=BFEMP&utm_content=F&utm_term=registrar%20marca&device=m&gad_source=1&gclid=CjwKCAiA9bq6BhAKEiwAH6bqoHkuZEWLCLAFiacQh0iJc-zlpE5MoTd5Qb5P65kWyHmq5Lx7DsQywxoCfgcQAvD_BwE", "score": 121, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-03T16:40:06-03:00"}, "createdAt": "2024-12-03T19:41:41.000Z", "Log": [{"timestamp": "2024-12-03T19:41:41.000Z"}, {"timestamp": "2024-12-03T19:41:45.000Z"}, {"timestamp": "2024-12-03T19:41:49.000Z"}, {"timestamp": "2024-12-03T19:41:56.000Z"}, {"timestamp": "2024-12-03T19:41:59.000Z"}, {"timestamp": "2024-12-03T19:42:14.000Z"}, {"timestamp": "2024-12-03T19:42:18.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43160962", "formData": {"brandTitle": "Gráfica Novo Tempo ", "leadName": "<PERSON><PERSON><PERSON><PERSON>", "leadNumber": "(48) 99207-4272", "leadMail": "j<PERSON><PERSON><PERSON>-and<PERSON>@bol.com.br", "businessInternship": "<PERSON><PERSON> atuo há mais de 6 meses", "businessCollaborators": "1 a 2", "haveCNPJ": "Sim.", "taxFramework": "MEI", "businessDefinition": "Outros", "businessDefinitionDescription": "Gráfica ", "clientSiteOrSocialLink": "Ainda não", "utmSource": ["Google "], "utmCampaign": "[\"PMBRMK\"]", "firstTimeUrl": "https://registre.se/?utm_source=Google+&utm_campaign=PMBRMK&gad_source=1&gclid=CjwKCAiA0rW6BhAcEiwAQH28IpBvl42-I6sUcbBs-UCmbWwlxO8soXVBtp2ZUXmlZHSCT4-vuigLrBoCu44QAvD_BwE", "score": 125, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-02T11:45:50-03:00"}, "createdAt": "2024-12-02T14:46:52.000Z", "Log": [{"timestamp": "2024-12-02T14:47:06.000Z"}, {"timestamp": "2024-12-02T14:47:12.000Z"}, {"timestamp": "2024-12-02T14:46:52.000Z"}, {"timestamp": "2024-12-02T14:47:15.000Z"}, {"timestamp": "2024-12-02T14:47:16.000Z"}, {"timestamp": "2024-12-02T14:47:20.000Z"}, {"timestamp": "2024-12-02T14:47:25.000Z"}, {"timestamp": "2024-12-02T14:47:30.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43215780", "formData": {"brandTitle": "M4 digital", "leadName": "<PERSON><PERSON><PERSON>", "leadNumber": "(11) 97616-2588", "leadMail": "<EMAIL>", "businessInternship": "Por enquanto é só um plano", "consultationPurpose": "Somente saber se está disponível.", "businessCollaborators": "1 a 2", "haveCNPJ": "Não, mas estamos providenciando.", "notCnpjTaxFramework": "MEI", "businessDefinition": "Prestação de serviço", "businessDefinitionDescription": "agencia de midias sociais", "clientSiteOrSocialLink": "Ainda não", "reasonRegistration": ["Curiosidade em saber como funciona"], "degreeOfKnowledge": "<PERSON><PERSON><PERSON>", "investmentInTheBrand": "Mais de 1 mil", "whenToStartProcess": "Em 1 ou 2 meses", "utmSource": ["Google"], "utmCampaign": "[\"BFKW\"]", "firstTimeUrl": "https://www.registre.se/?utm_source=Google&utm_campaign=BFKW&utm_content=F&utm_term=empresa%20marcas%20e%20patentes&device=c&gad_source=1&gclid=CjwKCAiA0rW6BhAcEiwAQH28IlEM901RahUxQmqHKKnV3LtFZBEiHbI5wzLZtAMgugRxiNoOGRswFxoCLSYQAvD_BwE", "score": 48, "utmFinal": null, "campaignHistory": "[\"BFKW\"]", "firstVisitTime": "2024-12-02T15:15:21-03:00"}, "createdAt": "2024-12-02T18:43:37.000Z", "Log": [{"timestamp": "2024-12-02T18:44:05.000Z"}, {"timestamp": "2024-12-02T18:44:08.000Z"}, {"timestamp": "2024-12-02T18:43:37.000Z"}, {"timestamp": "2024-12-02T18:43:43.000Z"}, {"timestamp": "2024-12-02T18:43:50.000Z"}, {"timestamp": "2024-12-02T18:43:57.000Z"}, {"timestamp": "2024-12-02T18:44:13.000Z"}, {"timestamp": "2024-12-02T18:44:25.000Z"}, {"timestamp": "2024-12-02T18:44:27.000Z"}, {"timestamp": "2024-12-02T18:44:35.000Z"}, {"timestamp": "2024-12-02T18:44:42.000Z"}, {"timestamp": "2024-12-02T18:44:45.000Z"}, {"timestamp": "2024-12-02T18:44:52.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43162305", "formData": {"brandTitle": "creques", "leadName": "Fábia Vitorino", "leadNumber": "(61) 99587-1917", "leadMail": "fabiavi<PERSON><PERSON>@gmail.com", "businessInternship": "<PERSON><PERSON> atuo há mais de 6 meses", "businessCollaborators": "1 a 2", "haveCNPJ": "Sim.", "taxFramework": "MEI", "businessDefinition": "Outros", "businessDefinitionDescription": "lanches", "clientSiteOrSocialLink": "<PERSON>m", "utmSource": ["Google"], "utmCampaign": "[\"BFEMP\"]", "firstTimeUrl": "https://www.registre.se/?utm_source=Google&utm_campaign=BFEMP&utm_content=F&utm_term=registro%20inpi&device=c&gad_source=1&gclid=CjwKCAiA0rW6BhAcEiwAQH28Il944QEUVSHbHhG6x-lTlqirMdzw9VMviAdbX69LJiYIWbjJP-0jDRoC2FsQAvD_BwE", "score": 145, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-02T12:22:39-03:00"}, "createdAt": "2024-12-02T15:23:48.000Z", "Log": [{"timestamp": "2024-12-02T15:23:48.000Z"}, {"timestamp": "2024-12-02T15:23:54.000Z"}, {"timestamp": "2024-12-02T15:23:59.000Z"}, {"timestamp": "2024-12-02T15:24:02.000Z"}, {"timestamp": "2024-12-02T15:24:05.000Z"}, {"timestamp": "2024-12-02T15:24:11.000Z"}, {"timestamp": "2024-12-02T15:24:19.000Z"}, {"timestamp": "2024-12-02T15:24:23.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43157949", "formData": {"brandTitle": "leilão usadão má<PERSON>as", "leadName": "<PERSON><PERSON>", "leadNumber": "(47) 99961-4077", "leadMail": "<EMAIL>", "businessInternship": "<PERSON><PERSON> atuo há mais de 2 anos", "businessCollaborators": "11 a 50", "haveCNPJ": "Sim.", "taxFramework": "DEMAIS", "utmSource": "", "utmCampaign": null, "firstTimeUrl": null, "score": 202, "utmFinal": null, "campaignHistory": null, "firstVisitTime": null}, "createdAt": "2024-12-02T13:55:17.000Z", "Log": [{"timestamp": "2024-12-02T13:55:36.000Z"}, {"timestamp": "2024-12-02T13:55:17.000Z"}, {"timestamp": "2024-12-02T13:55:32.000Z"}, {"timestamp": "2024-12-02T13:55:39.000Z"}, {"timestamp": "2024-12-02T13:55:44.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43166060", "formData": {"brandTitle": "Novo conceito", "leadName": "ariel", "leadNumber": "(19) 99404-2600", "leadMail": "<EMAIL>", "businessInternship": "Pretendo começar em breve", "consultationPurpose": "Confirmar se o nome está disponível, antes de criar o logotipo.", "businessCollaborators": "1 a 2", "haveCNPJ": "Atuo como pessoa física", "businessDefinition": "Outros", "businessDefinitionDescription": "roupas", "clientSiteOrSocialLink": "Ainda não", "reasonRegistration": ["Quero ganhar credibilidade"], "degreeOfKnowledge": "<PERSON><PERSON><PERSON>", "investmentInTheBrand": "Por enquanto nada", "whenToStartProcess": "Não tenho um prazo", "utmSource": ["parceiros"], "utmCampaign": "[\"gn-bradda\"]", "firstTimeUrl": "https://registre.se/bradda-gerador-de-nomes?utm_campaign=gn-bradda&utm_source=parceiros", "score": 66, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-02T14:45:21-03:00"}, "createdAt": "2024-12-02T17:46:50.000Z", "Log": [{"timestamp": "2024-12-02T17:46:50.000Z"}, {"timestamp": "2024-12-02T17:47:08.000Z"}, {"timestamp": "2024-12-02T17:47:17.000Z"}, {"timestamp": "2024-12-02T17:47:25.000Z"}, {"timestamp": "2024-12-02T17:47:32.000Z"}, {"timestamp": "2024-12-02T17:47:44.000Z"}, {"timestamp": "2024-12-02T17:47:56.000Z"}, {"timestamp": "2024-12-02T17:48:03.000Z"}, {"timestamp": "2024-12-02T17:48:20.000Z"}, {"timestamp": "2024-12-02T17:48:24.000Z"}, {"timestamp": "2024-12-02T17:48:29.000Z"}, {"timestamp": "2024-12-02T17:48:39.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43166129", "formData": {"brandTitle": "agrobar ", "leadName": "marco", "leadNumber": "(17) 99124-2600", "leadMail": "<EMAIL>", "utmSource": ["Google"], "utmCampaign": "[\"BFEMP\"]", "firstTimeUrl": "https://www.registre.se/?utm_source=Google&utm_campaign=BFEMP&utm_content=F&utm_term=registro%20inpi&device=c&gad_source=1&gclid=CjwKCAiA0rW6BhAcEiwAQH28IrpVd3mqmb2qkFWbVP352QBwlri-kf9BsDWleb6ea3cTAoE8SncxshoCwUwQAvD_BwE", "utmFinal": null, "campaignHistory": null, "score": 60, "firstVisitTime": "2024-12-02T14:45:51-03:00"}, "createdAt": "2024-12-02T17:48:54.000Z", "Log": [{"timestamp": "2024-12-02T17:48:54.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43176861", "formData": {"brandTitle": "Efs Soluções Elétricas", "leadName": "felipe", "leadNumber": "(35) 00000-0000", "leadMail": "<EMAIL>", "utmSource": ["Google "], "utmCampaign": "[\"PMBRMK\"]", "firstTimeUrl": "https://registre.se/?utm_source=Google+&utm_campaign=PMBRMK&gad_source=1&gclid=CjwKCAiA0rW6BhAcEiwAQH28Iof8vHAh0kIdsvRdKIe_jvhsaZ2rEBtj3TSs45xkKuYvaw_tGkjGERoCaf8QAvD_BwE", "utmFinal": null, "campaignHistory": null, "score": 40, "firstVisitTime": "2024-12-02T14:56:40-03:00"}, "createdAt": "2024-12-02T17:57:33.000Z", "Log": [{"timestamp": "2024-12-02T17:57:33.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43164208", "formData": {"brandTitle": "quenelle", "leadName": "<PERSON><PERSON><PERSON>", "leadNumber": "(47) 99733-4204", "leadMail": "<EMAIL>", "businessInternship": "Por enquanto é só um plano", "consultationPurpose": "Somente saber se está disponível.", "businessCollaborators": "1 a 2", "haveCNPJ": "Atuo como pessoa física", "businessDefinition": "Fabricação de produtos", "businessDefinitionDescription": "comida", "clientSiteOrSocialLink": "Ainda não", "utmSource": ["Google"], "utmCampaign": "[\"Tagg_Listas\"]", "firstTimeUrl": "https://registre.se/?utm_campaign=Tagg_Listas&utm_source=Google&utm_medium=Pesquisa&utm_content=GA_DSA&utm_keyword=&utm_adID=630221062241&utm_targetid=dsa-***********&utm_matchtype=&utm_local=9102301&utm_device=c&utm_network=g&gad_source=1&gclid=CjwKCAiA0rW6BhAcEiwAQH28Iur3YJBee3xBV0yNPsn5A1lMUL28IH5GVU3BEvOcGUpycOxUXvsK4RoCwgsQAvD_BwE", "score": 76, "utmFinal": null, "campaignHistory": null, "firstVisitTime": "2024-12-02T13:46:08-03:00"}, "createdAt": "2024-12-02T16:46:38.000Z", "Log": [{"timestamp": "2024-12-02T16:46:38.000Z"}, {"timestamp": "2024-12-02T16:46:44.000Z"}, {"timestamp": "2024-12-02T16:46:45.000Z"}, {"timestamp": "2024-12-02T16:46:50.000Z"}, {"timestamp": "2024-12-02T16:46:55.000Z"}, {"timestamp": "2024-12-02T16:47:06.000Z"}, {"timestamp": "2024-12-02T16:47:10.000Z"}, {"timestamp": "2024-12-02T16:47:13.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43215337", "formData": {"brandTitle": "Malu Cakes", "leadName": "<PERSON>", "leadNumber": "(11) 91360-2293", "leadMail": "<EMAIL>", "businessInternship": "Por enquanto é só um plano", "consultationPurpose": "Somente saber se está disponível.", "businessCollaborators": "1 a 2", "haveCNPJ": "Atuo como pessoa física", "businessDefinition": "Outros", "businessDefinitionDescription": "<PERSON><PERSON><PERSON><PERSON> ", "clientSiteOrSocialLink": "<PERSON>m", "clientLink": "<PERSON><PERSON>", "reasonRegistration": ["Curiosidade em saber como funciona"], "degreeOfKnowledge": "Já pesquisei em alguns sites sobre o assunto", "investmentInTheBrand": "Por enquanto nada", "utmSource": ["Google "], "utmCampaign": "[\"PMBRMK\"]", "firstTimeUrl": "https://registre.se/?utm_source=Google+&utm_campaign=PMBRMK&gad_source=1&gbraid=0AAAAAohu6STSKuWeXgOmN38y8GHRPd_6n&gclid=CjwKCAiA0rW6BhAcEiwAQH28Ir9f3PTeFt1ewIzc3u6pZ7QIekvpMbRs9aVtSGHPQKK5kC6uBoiwAhoCtzQQAvD_BwE", "utmFinal": null, "campaignHistory": null, "score": 36, "firstVisitTime": "2024-12-02T15:30:36-03:00"}, "createdAt": "2024-12-02T18:31:29.000Z", "Log": [{"timestamp": "2024-12-02T18:31:29.000Z"}, {"timestamp": "2024-12-02T18:31:37.000Z"}, {"timestamp": "2024-12-02T18:31:45.000Z"}, {"timestamp": "2024-12-02T18:31:49.000Z"}, {"timestamp": "2024-12-02T18:31:53.000Z"}, {"timestamp": "2024-12-02T18:32:02.000Z"}, {"timestamp": "2024-12-02T18:32:09.000Z"}, {"timestamp": "2024-12-02T18:32:13.000Z"}, {"timestamp": "2024-12-02T18:32:26.000Z"}, {"timestamp": "2024-12-02T18:32:44.000Z"}, {"timestamp": "2024-12-02T18:32:56.000Z"}, {"timestamp": "2024-12-02T18:33:00.000Z"}, {"timestamp": "2024-12-02T18:33:08.000Z"}, {"timestamp": "2024-12-02T18:33:09.000Z"}], "inRecovery": true, "stageName": "⛑️ Recuperação"}, {"crmId": "43148595", "formData": {"brandTitle": "Verona cosméticos ", "leadName": "<PERSON> ", "leadNumber": "(11) 96134-5653", "leadMail": "<EMAIL>", "businessInternship": "Pretendo começar em breve", "utmSource": "", "utmCampaign": null, "firstTimeUrl": null, "score": 35, "utmFinal": null, "campaignHistory": null, "firstVisitTime": null}, "createdAt": "2024-12-01T23:44:04.000Z", "Log": [{"timestamp": "2024-12-01T23:44:04.000Z"}, {"timestamp": "2024-12-01T23:44:12.000Z"}], "inRecovery": false, "stageName": "Entrada - Form Incompleto"}]