export interface LocalStorageData {
  crmId?: string;
  firstTimeUrl?: string;
  firstVisitTime?: string;
  utmHistory?: string[];
  [key: string]: any;
}

export interface Opportunity {
  leadName: string;
  leadMail: string;
  leadNumber: string;
  score: number;
}

export interface MarketingLead {
  id: number;
  timestamp: string;
  opportunityId: string | null;
  crmId: string;
  utmSource: string;
  utmMedium: string;
  utmCampaign: string;
  utmContent: string | null;
  utmTerm: string | null;
  campaignId: string;
  localStorageData: {
    '@crmId': number;
    loglevel: string;
    '@UTMFinal': string;
    '@utmMedium': string[];
    '@utmSource': string[];
    '@utmContent': string[];
    '@utmCampaign': string[];
    '@firstTimeUrl': string;
    cookiesConsent: string;
    '@firstVisitTime': string;
    lastExternalReferrer: string;
    '@utmMediumLastConversion': string[];
    '@utmSourceLastConversion': string[];
    lastExternalReferrerTime: number;
    '@utmContentLastConversion': string[];
    '@utmCampaignLastConversion': string[];
  };
  ipAddress: string;
  userAgent: string;
  Opportunity: {
    leadName?: string;
    leadMail?: string;
    leadNumber?: string;
    score?: number;
  } | null;
}

export interface MarketingLeadsResponse {
  data: MarketingLead[];
  total: number;
  page: number;
  totalPages: number;
}

export interface MarketingFilters {
  startDate?: string;
  endDate?: string;
  searchTerm?: string;
  page?: number;
} 